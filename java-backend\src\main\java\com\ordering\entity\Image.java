package com.ordering.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 图片实体类
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Entity
@Table(name = "images")
@TableName("images")
public class Image implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 图片名称
     */
    @NotBlank(message = "图片名称不能为空")
    @Column(name = "name", nullable = false, length = 255)
    private String name;

    /**
     * 原始文件名
     */
    @NotBlank(message = "原始文件名不能为空")
    @Column(name = "original_name", nullable = false, length = 255)
    private String originalName;

    /**
     * 文件路径
     */
    @NotBlank(message = "文件路径不能为空")
    @Column(name = "file_path", nullable = false, length = 500)
    private String filePath;

    /**
     * 访问URL
     */
    @NotBlank(message = "访问URL不能为空")
    @Column(name = "url", nullable = false, length = 500)
    private String url;

    /**
     * 缩略图路径
     */
    @Column(name = "thumbnail_path", length = 500)
    private String thumbnailPath;

    /**
     * 缩略图URL
     */
    @Column(name = "thumbnail_url", length = 500)
    private String thumbnailUrl;

    /**
     * 文件大小（字节）
     */
    @NotNull(message = "文件大小不能为空")
    @Column(name = "file_size", nullable = false)
    private Long fileSize;

    /**
     * 图片宽度
     */
    @Column(name = "width")
    private Integer width;

    /**
     * 图片高度
     */
    @Column(name = "height")
    private Integer height;

    /**
     * 文件类型/MIME类型
     */
    @NotBlank(message = "文件类型不能为空")
    @Column(name = "content_type", nullable = false, length = 100)
    private String contentType;

    /**
     * 文件扩展名
     */
    @NotBlank(message = "文件扩展名不能为空")
    @Column(name = "extension", nullable = false, length = 10)
    private String extension;

    /**
     * 图片分类（dish-菜品图片, banner-轮播图, avatar-头像, other-其他）
     */
    @Column(name = "category", length = 50)
    private String category;

    /**
     * 关联的业务ID（如菜品ID、用户ID等）
     */
    @Column(name = "business_id")
    private Long businessId;

    /**
     * 图片描述
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 排序字段
     */
    @Column(name = "sort_order")
    private Integer sortOrder;

    /**
     * 状态（0-禁用，1-启用）
     */
    @Column(name = "status", nullable = false)
    private Integer status = 1;

    /**
     * 是否删除（0-未删除，1-已删除）
     */
    @TableLogic
    @Column(name = "deleted", nullable = false)
    private Integer deleted = 0;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 创建者ID
     */
    @Column(name = "created_by")
    private Long createdBy;

    /**
     * 更新者ID
     */
    @Column(name = "updated_by")
    private Long updatedBy;
}
