# 图片资源说明

## 📁 图片文件夹结构

```
images/
├── README.md                    # 本说明文件
├── icon-guide.md               # 图标使用指南
├── create-icons.html           # 图标生成工具
├── placeholder.jpg             # 默认占位图
├── dish-default.jpg            # 菜品默认图片
├── empty-cart.png              # 空购物车图片
├── empty-dishes.png            # 空菜品列表图片
├── no-results.png              # 无搜索结果图片
├── default-avatar.png          # 默认头像
└── share.jpg                   # 分享图片
```

## 🎨 图片规格要求

### TabBar 图标
- **尺寸**: 81px × 81px (3倍图)
- **格式**: PNG格式，支持透明背景
- **当前方案**: 使用文字图标（Emoji），无需额外图片

### 菜品图片
- **尺寸**: 建议 400px × 400px 以上
- **格式**: JPG/PNG格式
- **比例**: 1:1 正方形比例最佳

### 轮播图
- **尺寸**: 750px × 300px (2.5:1比例)
- **格式**: JPG格式
- **文件大小**: 建议小于 200KB

### 头像图片
- **尺寸**: 200px × 200px
- **格式**: PNG格式，支持透明背景
- **形状**: 圆形或正方形

## 🔧 图片优化建议

### 1. 压缩优化
- 使用 TinyPNG 等工具压缩图片
- JPG格式质量设置为 80-90%
- PNG格式使用 8位色彩深度

### 2. 格式选择
- **照片类**: 使用 JPG 格式
- **图标类**: 使用 PNG 格式
- **简单图形**: 考虑使用 SVG 格式

### 3. 响应式图片
- 提供 1x、2x、3x 三种分辨率
- 使用 `mode="aspectFill"` 保持比例
- 设置合适的 `lazy-load` 属性

## 📱 使用示例

### 在 WXML 中使用
```xml
<!-- 菜品图片 -->
<image 
  class="dish-image" 
  src="{{item.image || '/images/dish-default.jpg'}}" 
  mode="aspectFill"
  lazy-load="{{true}}"
/>

<!-- 用户头像 -->
<image 
  class="user-avatar" 
  src="{{userInfo.avatar || '/images/default-avatar.png'}}" 
  mode="aspectFill"
/>

<!-- 空状态图片 -->
<image 
  class="empty-image" 
  src="/images/empty-cart.png" 
  mode="aspectFit"
/>
```

### 在 JS 中使用
```javascript
// 获取图片URL的工具函数
const { getImageUrl } = require('../utils/util')

// 使用示例
const imageUrl = getImageUrl(dish.image)
```

## 🎯 当前状态

### ✅ 已配置
- 文字图标系统（TabBar）
- 图片路径处理函数
- 默认占位图逻辑

### 📋 待添加（可选）
- 实际的图片文件
- 多分辨率图片
- 自定义图标文件

## 💡 提示

1. **开发阶段**: 可以使用网络图片进行测试
2. **生产环境**: 建议使用本地图片提升加载速度
3. **图片命名**: 使用有意义的文件名，便于维护
4. **版权注意**: 确保使用的图片有合法授权

## 🔗 相关文档

- [微信小程序图片组件文档](https://developers.weixin.qq.com/miniprogram/dev/component/image.html)
- [图片优化最佳实践](https://developers.weixin.qq.com/miniprogram/dev/framework/performance/tips.html)
- [TabBar图标设计规范](https://developers.weixin.qq.com/miniprogram/design/)
