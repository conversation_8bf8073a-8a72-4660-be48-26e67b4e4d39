-- 创建数据库
CREATE DATABASE IF NOT EXISTS `ordering_system` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `ordering_system`;

-- 创建图片表
CREATE TABLE IF NOT EXISTS `images` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) NOT NULL COMMENT '图片名称',
  `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `url` varchar(500) NOT NULL COMMENT '访问URL',
  `thumbnail_path` varchar(500) DEFAULT NULL COMMENT '缩略图路径',
  `thumbnail_url` varchar(500) DEFAULT NULL COMMENT '缩略图URL',
  `file_size` bigint(20) NOT NULL COMMENT '文件大小（字节）',
  `width` int(11) DEFAULT NULL COMMENT '图片宽度',
  `height` int(11) DEFAULT NULL COMMENT '图片高度',
  `content_type` varchar(100) NOT NULL COMMENT '文件类型/MIME类型',
  `extension` varchar(10) NOT NULL COMMENT '文件扩展名',
  `category` varchar(50) DEFAULT 'other' COMMENT '图片分类（dish-菜品图片, banner-轮播图, avatar-头像, other-其他）',
  `business_id` bigint(20) DEFAULT NULL COMMENT '关联的业务ID（如菜品ID、用户ID等）',
  `description` varchar(500) DEFAULT NULL COMMENT '图片描述',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序字段',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态（0-禁用，1-启用）',
  `deleted` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否删除（0-未删除，1-已删除）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建者ID',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新者ID',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted` (`deleted`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_category_business` (`category`, `business_id`),
  KEY `idx_url` (`url`(255))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片表';

-- 创建用户表（如果不存在）
CREATE TABLE IF NOT EXISTS `users` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `nickname` varchar(100) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `openid` varchar(100) DEFAULT NULL COMMENT '微信OpenID',
  `unionid` varchar(100) DEFAULT NULL COMMENT '微信UnionID',
  `gender` tinyint(4) DEFAULT 0 COMMENT '性别（0-未知，1-男，2-女）',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态（0-禁用，1-启用）',
  `deleted` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否删除（0-未删除，1-已删除）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_openid` (`openid`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 创建分类表（如果不存在）
CREATE TABLE IF NOT EXISTS `categories` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `icon` varchar(500) DEFAULT NULL COMMENT '分类图标URL',
  `description` varchar(500) DEFAULT NULL COMMENT '分类描述',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序字段',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态（0-禁用，1-启用）',
  `deleted` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否删除（0-未删除，1-已删除）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted` (`deleted`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类表';

-- 创建菜品表（如果不存在）
CREATE TABLE IF NOT EXISTS `dishes` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(200) NOT NULL COMMENT '菜品名称',
  `description` text COMMENT '菜品描述',
  `price` decimal(10,2) NOT NULL COMMENT '价格',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `image` varchar(500) DEFAULT NULL COMMENT '主图片URL',
  `images` text COMMENT '图片URL列表（逗号分隔）',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `sales_count` int(11) DEFAULT 0 COMMENT '销量',
  `rating` decimal(3,2) DEFAULT 5.00 COMMENT '评分',
  `is_hot` tinyint(4) DEFAULT 0 COMMENT '是否热门（0-否，1-是）',
  `is_new` tinyint(4) DEFAULT 0 COMMENT '是否新品（0-否，1-是）',
  `is_recommended` tinyint(4) DEFAULT 0 COMMENT '是否推荐（0-否，1-是）',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序字段',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态（0-下架，1-上架）',
  `deleted` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否删除（0-未删除，1-已删除）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted` (`deleted`),
  KEY `idx_is_hot` (`is_hot`),
  KEY `idx_is_new` (`is_new`),
  KEY `idx_is_recommended` (`is_recommended`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜品表';

-- 创建轮播图表（如果不存在）
CREATE TABLE IF NOT EXISTS `banners` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(200) DEFAULT NULL COMMENT '标题',
  `image` varchar(500) NOT NULL COMMENT '图片URL',
  `link_type` varchar(50) DEFAULT 'none' COMMENT '链接类型（none-无链接，dish-菜品详情，category-分类页面，url-外部链接）',
  `link_value` varchar(500) DEFAULT NULL COMMENT '链接值',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序字段',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态（0-禁用，1-启用）',
  `deleted` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否删除（0-未删除，1-已删除）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted` (`deleted`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='轮播图表';

-- 插入示例数据
INSERT IGNORE INTO `categories` (`id`, `name`, `icon`, `description`, `sort_order`) VALUES
(1, '川菜', '/uploads/category/chuancai.png', '麻辣鲜香的川菜', 1),
(2, '粤菜', '/uploads/category/yuecai.png', '清淡鲜美的粤菜', 2),
(3, '湘菜', '/uploads/category/xiangcai.png', '香辣可口的湘菜', 3),
(4, '鲁菜', '/uploads/category/lucai.png', '醇厚浓郁的鲁菜', 4),
(5, '苏菜', '/uploads/category/sucai.png', '清淡甜美的苏菜', 5),
(6, '浙菜', '/uploads/category/zhecai.png', '鲜嫩爽滑的浙菜', 6);

INSERT IGNORE INTO `dishes` (`id`, `name`, `description`, `price`, `original_price`, `image`, `category_id`, `sales_count`, `is_hot`, `is_new`) VALUES
(1, '麻婆豆腐', '经典川菜，麻辣鲜香，嫩滑爽口', 28.00, 35.00, '/uploads/dish/mapo_tofu.jpg', 1, 156, 1, 0),
(2, '宫保鸡丁', '传统川菜，酸甜微辣，鸡肉嫩滑', 32.00, NULL, '/uploads/dish/gongbao_chicken.jpg', 1, 89, 0, 1),
(3, '白切鸡', '经典粤菜，清淡鲜美，肉质鲜嫩', 45.00, NULL, '/uploads/dish/white_chicken.jpg', 2, 67, 0, 0),
(4, '剁椒鱼头', '湘菜名菜，香辣开胃，鱼肉鲜美', 68.00, 78.00, '/uploads/dish/fish_head.jpg', 3, 234, 1, 0);

INSERT IGNORE INTO `banners` (`id`, `title`, `image`, `link_type`, `link_value`, `sort_order`) VALUES
(1, '新品上市', '/uploads/banner/banner1.jpg', 'category', '1', 1),
(2, '限时优惠', '/uploads/banner/banner2.jpg', 'dish', '1', 2),
(3, '招牌菜品', '/uploads/banner/banner3.jpg', 'category', '2', 3),
(4, '特色推荐', '/uploads/banner/banner4.jpg', 'none', NULL, 4);
