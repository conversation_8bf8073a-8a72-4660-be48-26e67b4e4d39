const express = require('express')
const { query } = require('../config/database')
const { authenticateToken } = require('../middleware/auth')
const router = express.Router()

// 获取用户信息
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const users = await query(
      'SELECT id, phone, nickname, avatar, openid, gender, birthday, created_at FROM users WHERE id = ?',
      [req.user.id]
    )

    if (users.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      })
    }

    res.json({
      code: 0,
      message: '获取成功',
      data: users[0]
    })
  } catch (error) {
    console.error('获取用户信息错误:', error)
    res.status(500).json({
      code: 500,
      message: '获取用户信息失败'
    })
  }
})

// 更新用户信息
router.put('/profile', authenticateToken, async (req, res) => {
  try {
    const {
      nickname,
      avatar,
      gender,
      birthday
    } = req.body

    const updateFields = []
    const params = []

    if (nickname !== undefined) {
      updateFields.push('nickname = ?')
      params.push(nickname)
    }

    if (avatar !== undefined) {
      updateFields.push('avatar = ?')
      params.push(avatar)
    }

    if (gender !== undefined) {
      updateFields.push('gender = ?')
      params.push(gender)
    }

    if (birthday !== undefined) {
      updateFields.push('birthday = ?')
      params.push(birthday)
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '没有要更新的字段'
      })
    }

    params.push(req.user.id)

    await query(
      `UPDATE users SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
      params
    )

    // 返回更新后的用户信息
    const users = await query(
      'SELECT id, phone, nickname, avatar, openid, gender, birthday, created_at FROM users WHERE id = ?',
      [req.user.id]
    )

    res.json({
      code: 0,
      message: '更新成功',
      data: users[0]
    })
  } catch (error) {
    console.error('更新用户信息错误:', error)
    res.status(500).json({
      code: 500,
      message: '更新用户信息失败'
    })
  }
})

// 绑定手机号
router.post('/bind-phone', authenticateToken, async (req, res) => {
  try {
    const { phone, code } = req.body

    if (!phone || !code) {
      return res.status(400).json({
        code: 400,
        message: '手机号和验证码不能为空'
      })
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(phone)) {
      return res.status(400).json({
        code: 400,
        message: '手机号格式不正确'
      })
    }

    // 这里应该验证验证码，简化处理
    if (code !== '123456') {
      return res.status(400).json({
        code: 400,
        message: '验证码错误'
      })
    }

    // 检查手机号是否已被使用
    const existingUsers = await query(
      'SELECT id FROM users WHERE phone = ? AND id != ?',
      [phone, req.user.id]
    )

    if (existingUsers.length > 0) {
      return res.status(400).json({
        code: 400,
        message: '该手机号已被其他用户使用'
      })
    }

    // 更新用户手机号
    await query(
      'UPDATE users SET phone = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [phone, req.user.id]
    )

    res.json({
      code: 0,
      message: '手机号绑定成功'
    })
  } catch (error) {
    console.error('绑定手机号错误:', error)
    res.status(500).json({
      code: 500,
      message: '绑定手机号失败'
    })
  }
})

// 获取用户统计信息
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    // 获取订单统计
    const orderStats = await query(`
      SELECT 
        COUNT(*) as total_orders,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_orders,
        SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END) as total_spent
      FROM orders 
      WHERE user_id = ?
    `, [req.user.id])

    // 获取最近订单
    const recentOrders = await query(`
      SELECT id, order_no, total_amount, status, created_at
      FROM orders 
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT 5
    `, [req.user.id])

    res.json({
      code: 0,
      message: '获取成功',
      data: {
        orders: orderStats[0],
        recent_orders: recentOrders
      }
    })
  } catch (error) {
    console.error('获取用户统计错误:', error)
    res.status(500).json({
      code: 500,
      message: '获取用户统计失败'
    })
  }
})

module.exports = router
