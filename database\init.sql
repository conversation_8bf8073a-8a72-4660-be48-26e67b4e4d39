-- 创建数据库
CREATE DATABASE IF NOT EXISTS ordering_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE ordering_system;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  phone VARCHAR(11) UNIQUE,
  nickname VA<PERSON>HA<PERSON>(50),
  avatar VARCHAR(255),
  openid VARCHAR(100) UNIQUE,
  unionid VARCHAR(100),
  gender TINYINT DEFAULT 0 COMMENT '0:未知 1:男 2:女',
  birthday DATE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_phone (phone),
  INDEX idx_openid (openid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 分类表
CREATE TABLE IF NOT EXISTS categories (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VA<PERSON>HAR(50) NOT NULL,
  icon VARCHAR(255),
  sort_order INT DEFAULT 0,
  status TINYINT DEFAULT 1 COMMENT '0:禁用 1:启用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 菜品表
CREATE TABLE IF NOT EXISTS dishes (
  id INT PRIMARY KEY AUTO_INCREMENT,
  category_id INT NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL,
  image VARCHAR(255),
  is_hot TINYINT DEFAULT 0 COMMENT '是否热门',
  is_new TINYINT DEFAULT 0 COMMENT '是否新品',
  status TINYINT DEFAULT 1 COMMENT '0:下架 1:上架',
  sort_order INT DEFAULT 0,
  sales_count INT DEFAULT 0 COMMENT '销量',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
  INDEX idx_category (category_id),
  INDEX idx_status (status),
  INDEX idx_hot (is_hot),
  INDEX idx_new (is_new)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 订单表
CREATE TABLE IF NOT EXISTS orders (
  id INT PRIMARY KEY AUTO_INCREMENT,
  order_no VARCHAR(32) UNIQUE NOT NULL,
  user_id INT NOT NULL,
  total_amount DECIMAL(10,2) NOT NULL,
  status VARCHAR(20) DEFAULT 'pending' COMMENT 'pending:待确认 confirmed:已确认 preparing:制作中 completed:已完成 cancelled:已取消',
  remark TEXT COMMENT '备注',
  contact_name VARCHAR(50),
  contact_phone VARCHAR(11),
  delivery_address TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user (user_id),
  INDEX idx_status (status),
  INDEX idx_order_no (order_no)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 订单详情表
CREATE TABLE IF NOT EXISTS order_items (
  id INT PRIMARY KEY AUTO_INCREMENT,
  order_id INT NOT NULL,
  dish_id INT NOT NULL,
  dish_name VARCHAR(100) NOT NULL,
  dish_price DECIMAL(10,2) NOT NULL,
  quantity INT NOT NULL,
  subtotal DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
  FOREIGN KEY (dish_id) REFERENCES dishes(id) ON DELETE CASCADE,
  INDEX idx_order (order_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 轮播图表
CREATE TABLE IF NOT EXISTS banners (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(100),
  image VARCHAR(255) NOT NULL,
  link_type VARCHAR(20) COMMENT 'dish:菜品 category:分类 url:链接',
  link_id INT COMMENT '关联ID',
  link_url VARCHAR(255) COMMENT '链接地址',
  sort_order INT DEFAULT 0,
  status TINYINT DEFAULT 1 COMMENT '0:禁用 1:启用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入示例数据

-- 分类数据
INSERT INTO categories (name, icon, sort_order) VALUES
('热菜', '/images/categories/hot-dish.png', 100),
('凉菜', '/images/categories/cold-dish.png', 90),
('汤类', '/images/categories/soup.png', 80),
('主食', '/images/categories/staple.png', 70),
('饮品', '/images/categories/drink.png', 60),
('甜品', '/images/categories/dessert.png', 50);

-- 菜品数据
INSERT INTO dishes (category_id, name, description, price, image, is_hot, is_new, sort_order, sales_count) VALUES
-- 热菜
(1, '宫保鸡丁', '经典川菜，鸡肉嫩滑，花生香脆', 28.00, '/images/dishes/gongbao-chicken.jpg', 1, 0, 100, 156),
(1, '麻婆豆腐', '四川传统名菜，麻辣鲜香', 18.00, '/images/dishes/mapo-tofu.jpg', 1, 0, 95, 134),
(1, '红烧肉', '肥而不腻，入口即化', 35.00, '/images/dishes/braised-pork.jpg', 1, 0, 90, 98),
(1, '糖醋里脊', '酸甜可口，外酥内嫩', 32.00, '/images/dishes/sweet-sour-pork.jpg', 0, 1, 85, 76),
(1, '鱼香肉丝', '川菜经典，咸甜酸辣', 25.00, '/images/dishes/yuxiang-pork.jpg', 1, 0, 80, 112),

-- 凉菜
(2, '口水鸡', '麻辣鲜香，口感嫩滑', 22.00, '/images/dishes/saliva-chicken.jpg', 1, 0, 100, 89),
(2, '凉拌黄瓜', '清爽解腻，爽脆可口', 8.00, '/images/dishes/cucumber-salad.jpg', 0, 0, 95, 67),
(2, '蒜泥白肉', '肥瘦相间，蒜香浓郁', 26.00, '/images/dishes/garlic-pork.jpg', 0, 0, 90, 45),
(2, '拍黄瓜', '简单清爽，开胃小菜', 6.00, '/images/dishes/smashed-cucumber.jpg', 0, 0, 85, 78),

-- 汤类
(3, '西红柿鸡蛋汤', '酸甜开胃，营养丰富', 12.00, '/images/dishes/tomato-egg-soup.jpg', 0, 0, 100, 134),
(3, '冬瓜排骨汤', '清淡鲜美，滋补养生', 28.00, '/images/dishes/wintermelon-soup.jpg', 1, 0, 95, 67),
(3, '紫菜蛋花汤', '清淡鲜美，制作简单', 10.00, '/images/dishes/seaweed-soup.jpg', 0, 0, 90, 89),

-- 主食
(4, '扬州炒饭', '粒粒分明，香味浓郁', 15.00, '/images/dishes/yangzhou-rice.jpg', 1, 0, 100, 156),
(4, '牛肉面', '汤浓面劲，牛肉鲜美', 18.00, '/images/dishes/beef-noodles.jpg', 1, 0, 95, 123),
(4, '小笼包', '皮薄馅大，汤汁鲜美', 16.00, '/images/dishes/xiaolongbao.jpg', 0, 1, 90, 98),
(4, '白米饭', '优质大米，香甜可口', 3.00, '/images/dishes/white-rice.jpg', 0, 0, 85, 234),

-- 饮品
(5, '鲜榨橙汁', '新鲜橙子现榨，维C丰富', 12.00, '/images/dishes/orange-juice.jpg', 0, 1, 100, 67),
(5, '柠檬蜂蜜茶', '酸甜清香，美容养颜', 15.00, '/images/dishes/lemon-tea.jpg', 1, 0, 95, 89),
(5, '可乐', '经典碳酸饮料', 6.00, '/images/dishes/cola.jpg', 0, 0, 90, 145),
(5, '绿茶', '清香淡雅，解腻消食', 8.00, '/images/dishes/green-tea.jpg', 0, 0, 85, 78),

-- 甜品
(6, '红豆沙', '香甜软糯，传统甜品', 8.00, '/images/dishes/red-bean-soup.jpg', 0, 0, 100, 56),
(6, '芒果布丁', '香甜顺滑，果香浓郁', 12.00, '/images/dishes/mango-pudding.jpg', 0, 1, 95, 43),
(6, '提拉米苏', '意式经典，层次丰富', 25.00, '/images/dishes/tiramisu.jpg', 1, 1, 90, 34),
(6, '双皮奶', '奶香浓郁，口感顺滑', 10.00, '/images/dishes/double-skin-milk.jpg', 0, 0, 85, 67);

-- 轮播图数据
INSERT INTO banners (title, image, link_type, link_id, sort_order) VALUES
('新品上市', '/images/banners/banner1.jpg', 'category', 1, 100),
('特价优惠', '/images/banners/banner2.jpg', 'dish', 1, 90),
('招牌菜品', '/images/banners/banner3.jpg', 'category', 2, 80);

-- 创建测试用户
INSERT INTO users (phone, nickname, avatar) VALUES
('13800138000', '测试用户', '/images/avatars/default.png');
