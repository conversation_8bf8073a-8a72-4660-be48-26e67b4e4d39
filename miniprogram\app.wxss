/**app.wxss**/

/* 全局CSS变量 */
page {
  --primary-color: #ff6b35;
  --primary-gradient: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  --secondary-color: #667eea;
  --secondary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --success-color: #2ed573;
  --warning-color: #ffa502;
  --error-color: #ff4757;
  --text-primary: #2f3542;
  --text-secondary: #57606f;
  --text-light: #999;
  --background-primary: #ffffff;
  --background-secondary: #f5f5f5;
  --border-color: rgba(0, 0, 0, 0.1);
  --shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
  --radius-small: 8rpx;
  --radius-medium: 16rpx;
  --radius-large: 24rpx;
  --radius-round: 50rpx;
}

/* 全局重置样式 */
* {
  box-sizing: border-box;
}

page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--background-secondary);
}

/* 通用容器 */
.container {
  width: 100%;
  min-height: 100vh;
}

/* 通用按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border: none;
  border-radius: var(--radius-round);
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn:active {
  transform: scale(0.98);
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 53, 0.4);
}

.btn-secondary {
  background: var(--secondary-gradient);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.4);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
}

.btn-outline:active {
  background: var(--primary-color);
  color: white;
}

.btn-ghost {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.btn-large {
  padding: 30rpx 60rpx;
  font-size: 32rpx;
}

.btn-disabled {
  background: #ccc !important;
  color: #999 !important;
  box-shadow: none !important;
  border-color: #ccc !important;
}

/* 通用卡片样式 */
.card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-medium);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.card-body {
  padding: 30rpx;
}

.card-footer {
  padding: 30rpx;
  border-top: 1rpx solid var(--border-color);
  background: rgba(0, 0, 0, 0.02);
}

/* 通用输入框样式 */
.input {
  width: 100%;
  height: 80rpx;
  padding: 0 30rpx;
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-medium);
  font-size: 28rpx;
  background: var(--background-primary);
  transition: all 0.3s ease;
}

.input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4rpx rgba(255, 107, 53, 0.1);
}

.input-group {
  position: relative;
  margin-bottom: 30rpx;
}

.input-label {
  display: block;
  margin-bottom: 15rpx;
  font-size: 26rpx;
  color: var(--text-secondary);
  font-weight: 500;
}

.input-error {
  border-color: var(--error-color);
}

.input-success {
  border-color: var(--success-color);
}

/* 通用文本样式 */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-light {
  color: var(--text-light);
}

.text-white {
  color: white;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-bold {
  font-weight: bold;
}

.text-medium {
  font-weight: 500;
}

.text-small {
  font-size: 24rpx;
}

.text-large {
  font-size: 32rpx;
}

.text-xl {
  font-size: 36rpx;
}

.text-xxl {
  font-size: 48rpx;
}

/* 通用间距 */
.m-0 { margin: 0; }
.m-1 { margin: 10rpx; }
.m-2 { margin: 20rpx; }
.m-3 { margin: 30rpx; }
.m-4 { margin: 40rpx; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 10rpx; }
.mt-2 { margin-top: 20rpx; }
.mt-3 { margin-top: 30rpx; }
.mt-4 { margin-top: 40rpx; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10rpx; }
.mb-2 { margin-bottom: 20rpx; }
.mb-3 { margin-bottom: 30rpx; }
.mb-4 { margin-bottom: 40rpx; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 10rpx; }
.ml-2 { margin-left: 20rpx; }
.ml-3 { margin-left: 30rpx; }
.ml-4 { margin-left: 40rpx; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 10rpx; }
.mr-2 { margin-right: 20rpx; }
.mr-3 { margin-right: 30rpx; }
.mr-4 { margin-right: 40rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 10rpx; }
.p-2 { padding: 20rpx; }
.p-3 { padding: 30rpx; }
.p-4 { padding: 40rpx; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 10rpx; }
.pt-2 { padding-top: 20rpx; }
.pt-3 { padding-top: 30rpx; }
.pt-4 { padding-top: 40rpx; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 10rpx; }
.pb-2 { padding-bottom: 20rpx; }
.pb-3 { padding-bottom: 30rpx; }
.pb-4 { padding-bottom: 40rpx; }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: 10rpx; }
.pl-2 { padding-left: 20rpx; }
.pl-3 { padding-left: 30rpx; }
.pl-4 { padding-left: 40rpx; }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: 10rpx; }
.pr-2 { padding-right: 20rpx; }
.pr-3 { padding-right: 30rpx; }
.pr-4 { padding-right: 40rpx; }

/* 通用布局 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-start {
  justify-content: flex-start;
}

.flex-end {
  justify-content: flex-end;
}

.align-center {
  align-items: center;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

.flex-1 {
  flex: 1;
}

.flex-wrap {
  flex-wrap: wrap;
}

/* 通用显示 */
.block {
  display: block;
}

.inline {
  display: inline;
}

.inline-block {
  display: inline-block;
}

.hidden {
  display: none;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

/* 通用定位 */
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

/* 通用圆角 */
.rounded-none {
  border-radius: 0;
}

.rounded-small {
  border-radius: var(--radius-small);
}

.rounded-medium {
  border-radius: var(--radius-medium);
}

.rounded-large {
  border-radius: var(--radius-large);
}

.rounded-full {
  border-radius: 50%;
}

/* 通用阴影 */
.shadow-none {
  box-shadow: none;
}

.shadow-light {
  box-shadow: var(--shadow-light);
}

.shadow-medium {
  box-shadow: var(--shadow-medium);
}

.shadow-heavy {
  box-shadow: var(--shadow-heavy);
}

/* 通用动画 */
.transition {
  transition: all 0.3s ease;
}

.transition-fast {
  transition: all 0.15s ease;
}

.transition-slow {
  transition: all 0.5s ease;
}

/* 通用背景 */
.bg-primary {
  background: var(--primary-color);
}

.bg-primary-gradient {
  background: var(--primary-gradient);
}

.bg-secondary {
  background: var(--secondary-color);
}

.bg-secondary-gradient {
  background: var(--secondary-gradient);
}

.bg-white {
  background: white;
}

.bg-transparent {
  background: transparent;
}

/* 通用边框 */
.border {
  border: 1rpx solid var(--border-color);
}

.border-primary {
  border: 1rpx solid var(--primary-color);
}

.border-none {
  border: none;
}

/* 响应式工具类 */
@media (max-width: 750rpx) {
  .hidden-mobile {
    display: none !important;
  }
}

@media (min-width: 751rpx) {
  .hidden-desktop {
    display: none !important;
  }
}

/* 安全区域适配 */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-top {
  padding-top: env(safe-area-inset-top);
}
