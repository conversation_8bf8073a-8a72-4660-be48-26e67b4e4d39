# 微信小程序在线点餐系统使用说明

## 项目概述

这是一个完整的微信小程序在线点餐系统，包含前端小程序和后端API服务，实现了从浏览菜品到下单支付的完整流程。

## 系统架构

```
微信小程序在线点餐系统
├── 前端 (miniprogram/)
│   ├── 微信小程序原生框架
│   ├── 用户界面和交互逻辑
│   └── 与后端API通信
├── 后端 (backend/)
│   ├── Node.js + Express
│   ├── RESTful API设计
│   └── JWT身份认证
└── 数据库 (MySQL)
    ├── 用户管理
    ├── 商品管理
    └── 订单管理
```

## 功能特性

### 用户端功能
- ✅ 用户注册/登录（手机号验证码 + 微信授权）
- ✅ 首页轮播图和推荐菜品
- ✅ 菜品分类浏览
- ✅ 菜品搜索功能
- ✅ 菜品详情查看
- ✅ 购物车管理
- ✅ 订单创建和管理
- ✅ 个人信息管理
- ✅ 订单状态跟踪

### 管理功能
- ✅ 菜品管理（增删改查）
- ✅ 分类管理
- ✅ 订单管理
- ✅ 用户管理
- ✅ 轮播图管理

## 快速开始

### 环境准备
1. **Node.js** (>= 14.0.0)
2. **MySQL** (>= 5.7)
3. **微信开发者工具**

### 安装步骤

#### 方法一：一键启动（推荐）
```bash
# Windows用户
双击运行 start.bat

# Linux/Mac用户
chmod +x start.sh
./start.sh
```

#### 方法二：手动安装
1. **配置数据库**
   ```bash
   # 1. 创建MySQL数据库
   # 2. 修改 backend/.env 文件中的数据库配置
   # 3. 初始化数据库
   cd backend
   npm install
   node scripts/init-db.js
   ```

2. **启动后端服务**
   ```bash
   cd backend
   npm start
   ```

3. **配置小程序**
   - 打开微信开发者工具
   - 导入项目，选择 `miniprogram` 目录
   - 配置AppID（可使用测试号）
   - 编译运行

## 配置说明

### 后端配置 (backend/.env)
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=ordering_system

# JWT配置
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=7d

# 微信小程序配置
WECHAT_APP_ID=your-app-id
WECHAT_APP_SECRET=your-app-secret
```

### 小程序配置
修改 `miniprogram/app.js` 中的后端地址：
```javascript
globalData: {
  baseUrl: 'http://localhost:3000/api'
}
```

## 测试账号

### 开发环境测试
- **手机号**: 13800138000
- **验证码**: 123456（开发环境固定）

### 数据库测试数据
系统会自动创建以下测试数据：
- 6个菜品分类
- 25个示例菜品
- 4个轮播图
- 3个测试用户

## 主要页面说明

### 1. 首页 (pages/index/)
- 搜索功能
- 轮播图展示
- 分类导航
- 热门菜品
- 新品推荐

### 2. 分类页 (pages/category/)
- 分类筛选
- 菜品列表
- 搜索功能
- 分页加载

### 3. 菜品详情 (pages/detail/)
- 菜品图片预览
- 详细信息展示
- 数量选择
- 加入购物车/立即购买
- 相关推荐

### 4. 购物车 (pages/cart/)
- 商品列表
- 数量调整
- 全选/取消全选
- 结算功能

### 5. 订单确认 (pages/order/)
- 配送信息填写
- 商品确认
- 备注信息
- 订单提交

### 6. 个人中心 (pages/profile/)
- 用户信息
- 订单统计
- 功能菜单
- 退出登录

## API接口说明

### 认证相关
- `POST /api/auth/send-code` - 发送验证码
- `POST /api/auth/login` - 手机号登录
- `POST /api/auth/wechat-login` - 微信登录

### 菜品相关
- `GET /api/dishes` - 获取菜品列表
- `GET /api/dishes/hot` - 获取热门菜品
- `GET /api/dishes/new` - 获取新品推荐
- `GET /api/dishes/:id` - 获取菜品详情

### 分类相关
- `GET /api/categories` - 获取分类列表
- `GET /api/categories/:id` - 获取分类详情

### 订单相关
- `POST /api/orders` - 创建订单
- `GET /api/orders` - 获取订单列表
- `GET /api/orders/:id` - 获取订单详情
- `PUT /api/orders/:id/cancel` - 取消订单

## 开发注意事项

1. **数据库连接**
   - 确保MySQL服务正在运行
   - 检查数据库配置是否正确

2. **小程序开发**
   - 真机调试需要配置合法域名
   - 微信登录需要真实的AppID和AppSecret

3. **安全配置**
   - 生产环境请修改JWT密钥
   - 修改数据库默认密码

4. **图片资源**
   - 示例中的图片路径需要替换为真实图片
   - 建议使用CDN存储图片资源

## 常见问题

### Q: 数据库连接失败？
A: 检查MySQL服务是否启动，数据库配置是否正确

### Q: 小程序无法请求接口？
A: 检查后端服务是否启动，小程序中的baseUrl是否正确

### Q: 验证码收不到？
A: 开发环境使用固定验证码123456，生产环境需要配置短信服务

### Q: 微信登录失败？
A: 需要配置真实的微信小程序AppID和AppSecret

## 技术支持

如有问题，请检查：
1. 控制台错误信息
2. 网络连接状态
3. 配置文件是否正确
4. 数据库服务状态

## 项目结构
```
微信小程序期末/
├── miniprogram/          # 小程序前端
│   ├── pages/           # 页面文件
│   ├── utils/           # 工具函数
│   ├── images/          # 图片资源
│   ├── app.js           # 小程序入口
│   ├── app.json         # 小程序配置
│   └── app.wxss         # 全局样式
├── backend/             # 后端服务
│   ├── routes/          # 路由文件
│   ├── config/          # 配置文件
│   ├── middleware/      # 中间件
│   ├── scripts/         # 脚本文件
│   ├── app.js           # 服务器入口
│   └── package.json     # 依赖配置
├── database/            # 数据库脚本
├── start.bat           # Windows启动脚本
├── start.sh            # Linux/Mac启动脚本
└── README.md           # 项目说明
```

祝您使用愉快！🎉
