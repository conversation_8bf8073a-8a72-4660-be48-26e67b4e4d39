// pages/debug/debug.js
const { get, post } = require('../../utils/request')
const { showLoading, hideLoading, showToast, getSystemInfo, checkNetworkStatus } = require('../../utils/util')

Page({
  data: {
    systemInfo: {},
    networkStatus: '',
    apiStatus: {
      backend: 'unknown',
      database: 'unknown'
    },
    testResults: [],
    cartData: {},
    userInfo: {},
    debugLogs: []
  },

  onLoad() {
    this.initDebugInfo()
  },

  // 初始化调试信息
  async initDebugInfo() {
    try {
      // 获取系统信息
      const systemInfo = await getSystemInfo()
      this.setData({ systemInfo })

      // 检查网络状态
      const networkStatus = await checkNetworkStatus()
      this.setData({ 
        networkStatus: networkStatus ? '正常' : '无网络' 
      })

      // 获取应用数据
      this.loadAppData()

      // 测试API连接
      this.testApiConnection()

    } catch (error) {
      this.addLog('初始化调试信息失败: ' + error.message)
    }
  },

  // 加载应用数据
  loadAppData() {
    const app = getApp()
    
    // 获取购物车数据
    const cartData = app.getCartData()
    this.setData({ cartData })

    // 获取用户信息
    const userInfo = app.getUserInfo()
    this.setData({ userInfo: userInfo || {} })

    this.addLog('应用数据加载完成')
  },

  // 测试API连接
  async testApiConnection() {
    const tests = [
      { name: '获取分类列表', api: '/categories' },
      { name: '获取菜品列表', api: '/dishes?limit=5' },
      { name: '获取热门菜品', api: '/dishes/hot?limit=3' },
      { name: '获取轮播图', api: '/banners' }
    ]

    const results = []

    for (const test of tests) {
      try {
        showLoading(`测试${test.name}...`)
        const startTime = Date.now()
        
        const res = await get(test.api)
        const endTime = Date.now()
        const duration = endTime - startTime

        results.push({
          name: test.name,
          status: 'success',
          duration: duration + 'ms',
          data: res.data
        })

        this.addLog(`✅ ${test.name} - 成功 (${duration}ms)`)

      } catch (error) {
        results.push({
          name: test.name,
          status: 'error',
          error: error.message
        })

        this.addLog(`❌ ${test.name} - 失败: ${error.message}`)
      }
    }

    hideLoading()
    this.setData({ 
      testResults: results,
      'apiStatus.backend': results.every(r => r.status === 'success') ? 'success' : 'error'
    })
  },

  // 测试购物车功能
  testCartFunction() {
    const app = getApp()
    
    try {
      // 测试添加商品
      app.addToCart({
        id: 999,
        name: '测试菜品',
        price: 19.99,
        image: '/images/test.jpg',
        quantity: 2
      })

      // 测试更新数量
      app.updateCartItem(999, 3)

      // 测试删除商品
      app.removeFromCart(999)

      this.addLog('✅ 购物车功能测试通过')
      this.loadAppData()

    } catch (error) {
      this.addLog('❌ 购物车功能测试失败: ' + error.message)
    }
  },

  // 测试本地存储
  testLocalStorage() {
    try {
      const testKey = 'debug_test'
      const testData = { 
        timestamp: Date.now(), 
        data: '测试数据' 
      }

      // 测试写入
      wx.setStorageSync(testKey, testData)
      
      // 测试读取
      const readData = wx.getStorageSync(testKey)
      
      if (JSON.stringify(readData) === JSON.stringify(testData)) {
        this.addLog('✅ 本地存储测试通过')
      } else {
        this.addLog('❌ 本地存储数据不一致')
      }

      // 清理测试数据
      wx.removeStorageSync(testKey)

    } catch (error) {
      this.addLog('❌ 本地存储测试失败: ' + error.message)
    }
  },

  // 测试网络请求
  async testNetworkRequest() {
    try {
      showLoading('测试网络请求...')
      
      const startTime = Date.now()
      const res = await get('/categories')
      const endTime = Date.now()
      
      this.addLog(`✅ 网络请求测试通过 - 响应时间: ${endTime - startTime}ms`)
      
    } catch (error) {
      this.addLog('❌ 网络请求测试失败: ' + error.message)
    } finally {
      hideLoading()
    }
  },

  // 清空购物车
  clearCart() {
    const app = getApp()
    app.clearCart()
    this.loadAppData()
    this.addLog('🗑️ 购物车已清空')
  },

  // 清空调试日志
  clearLogs() {
    this.setData({ debugLogs: [] })
  },

  // 导出调试信息
  exportDebugInfo() {
    const debugInfo = {
      timestamp: new Date().toISOString(),
      systemInfo: this.data.systemInfo,
      networkStatus: this.data.networkStatus,
      apiStatus: this.data.apiStatus,
      testResults: this.data.testResults,
      cartData: this.data.cartData,
      userInfo: this.data.userInfo,
      debugLogs: this.data.debugLogs
    }

    const debugString = JSON.stringify(debugInfo, null, 2)
    
    wx.setClipboardData({
      data: debugString,
      success: () => {
        showToast('调试信息已复制到剪贴板')
      }
    })
  },

  // 添加日志
  addLog(message) {
    const timestamp = new Date().toLocaleTimeString()
    const log = `[${timestamp}] ${message}`
    
    this.setData({
      debugLogs: [...this.data.debugLogs, log]
    })
  },

  // 查看详细信息
  onViewDetail(e) {
    const { type, data } = e.currentTarget.dataset
    
    wx.showModal({
      title: `${type}详细信息`,
      content: JSON.stringify(data, null, 2),
      showCancel: false
    })
  },

  // 重新测试
  onRetest() {
    this.setData({
      testResults: [],
      debugLogs: []
    })
    this.initDebugInfo()
  }
})
