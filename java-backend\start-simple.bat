@echo off
echo ========================================
echo   启动在线点餐系统后端服务
echo ========================================

echo 正在检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误：Java环境未配置，请安装Java 8或更高版本
    pause
    exit /b 1
)

echo 正在检查Maven环境...
mvn -version
if %errorlevel% neq 0 (
    echo 错误：Maven环境未配置，请安装Maven
    pause
    exit /b 1
)

echo 正在清理并编译项目...
mvn clean compile
if %errorlevel% neq 0 (
    echo 错误：项目编译失败
    pause
    exit /b 1
)

echo 正在启动应用...
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Dserver.port=8080"

pause
