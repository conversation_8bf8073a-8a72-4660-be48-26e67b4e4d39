<!--pages/menu/menu.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input" bindtap="onSearchTap">
      <icon class="search-icon" type="search" size="16" color="#999"></icon>
      <text class="search-placeholder">搜索菜品</text>
      <text class="search-btn" wx:if="{{searchKeyword}}" bindtap="onSearchConfirm">搜索</text>
    </view>
  </view>

  <view class="main-content">
    <!-- 左侧分类 -->
    <scroll-view class="category-sidebar" scroll-y="{{true}}">
      <view 
        class="category-item {{selectedCategory === item.id ? 'active' : ''}}"
        wx:for="{{categories}}" 
        wx:key="id"
        bindtap="onCategorySelect"
        data-category="{{item}}"
      >
        <text class="category-name">{{item.name}}</text>
        <view class="category-count" wx:if="{{item.dishCount > 0}}">{{item.dishCount}}</view>
      </view>
    </scroll-view>

    <!-- 右侧菜品列表 -->
    <scroll-view 
      class="dish-content" 
      scroll-y="{{true}}"
      bindscrolltolower="onLoadMore"
      refresher-enabled="{{true}}"
      refresher-triggered="{{refreshing}}"
      bindrefresherrefresh="onRefresh"
    >
      <!-- 分类标题 -->
      <view class="category-header" wx:if="{{currentCategoryName}}">
        <text class="category-title">{{currentCategoryName}}</text>
        <text class="dish-count">共{{dishes.length}}道菜</text>
      </view>

      <!-- 菜品列表 -->
      <view class="dish-list">
        <view 
          class="dish-item" 
          wx:for="{{dishes}}" 
          wx:key="id"
          bindtap="onDishTap"
          data-dish="{{item}}"
        >
          <image class="dish-image" src="{{item.image}}" mode="aspectFill" />
          <view class="dish-info">
            <view class="dish-header">
              <text class="dish-name">{{item.name}}</text>
              <view class="dish-tags">
                <text class="tag tag-hot" wx:if="{{item.isHot}}">热</text>
                <text class="tag tag-new" wx:if="{{item.isNew}}">新</text>
                <text class="tag tag-recommend" wx:if="{{item.isRecommended}}">荐</text>
              </view>
            </view>
            <text class="dish-desc">{{item.description}}</text>
            <view class="dish-meta">
              <text class="dish-sales">月销 {{item.salesCount}}</text>
              <text class="dish-rating">★ {{item.rating}}</text>
            </view>
            <view class="dish-footer">
              <view class="price-section">
                <text class="dish-price">
                  <text class="price-symbol">¥</text>{{item.price}}
                </text>
                <text class="original-price" wx:if="{{item.originalPrice && item.originalPrice > item.price}}">
                  ¥{{item.originalPrice}}
                </text>
              </view>
              
              <!-- 数量控制器 -->
              <view class="quantity-control" wx:if="{{item.cartQuantity > 0}}">
                <view 
                  class="quantity-btn" 
                  bindtap="onDecreaseQuantity"
                  data-dish="{{item}}"
                >
                  <icon type="minus" size="14" color="#ff6b35"></icon>
                </view>
                <text class="quantity-text">{{item.cartQuantity}}</text>
                <view 
                  class="quantity-btn" 
                  bindtap="onIncreaseQuantity"
                  data-dish="{{item}}"
                >
                  <icon type="plus" size="14" color="#ff6b35"></icon>
                </view>
              </view>
              
              <!-- 添加按钮 -->
              <view 
                class="add-btn" 
                wx:else
                bindtap="onAddToCart"
                data-dish="{{item}}"
              >
                <icon type="plus" size="16" color="#fff"></icon>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{hasMore}}">
        <text class="load-text">{{loadingMore ? '加载中...' : '上拉加载更多'}}</text>
      </view>

      <!-- 没有更多 -->
      <view class="no-more" wx:if="{{!hasMore && dishes.length > 0}}">
        <text class="no-more-text">没有更多菜品了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty" wx:if="{{!loading && dishes.length === 0}}">
        <image class="empty-icon" src="/images/empty-dish.png" />
        <text class="empty-text">暂无菜品</text>
        <text class="empty-desc">换个分类试试吧</text>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-area"></view>
    </scroll-view>
  </view>

  <!-- 购物车栏 -->
  <view class="cart-bar" wx:if="{{cartTotal > 0}}">
    <view class="cart-info" bindtap="onCartTap">
      <view class="cart-icon-wrapper">
        <icon class="cart-icon" type="success" size="24" color="#fff"></icon>
        <text class="cart-count">{{cartCount}}</text>
      </view>
      <view class="cart-text">
        <text class="cart-total">¥{{cartTotal}}</text>
        <text class="delivery-fee">配送费¥{{deliveryFee}}</text>
      </view>
    </view>
    <view 
      class="checkout-btn {{cartTotal >= minOrderAmount ? 'active' : 'disabled'}}"
      bindtap="onCheckout"
    >
      <text class="checkout-text">
        {{cartTotal >= minOrderAmount ? '去结算' : '还差¥' + (minOrderAmount - cartTotal)}}
      </text>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-overlay" wx:if="{{loading}}">
  <view class="loading-content">
    <text>加载中...</text>
  </view>
</view>
