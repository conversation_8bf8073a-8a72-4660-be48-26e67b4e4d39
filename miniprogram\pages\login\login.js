// pages/login/login.js
const { post } = require('../../utils/request')
const { showLoading, hideLoading, validatePhone } = require('../../utils/util')

Page({
  data: {
    loginType: 'phone', // phone, wechat
    phone: '',
    code: '',
    countdown: 0,
    canSendCode: true,
    agreementChecked: true,
    loading: false
  },

  onLoad(options) {
    const { redirect } = options
    if (redirect) {
      this.redirectUrl = decodeURIComponent(redirect)
    }
  },

  // 切换登录方式
  onSwitchLoginType(e) {
    const { type } = e.currentTarget.dataset
    this.setData({
      loginType: type,
      phone: '',
      code: '',
      countdown: 0,
      canSendCode: true
    })
  },

  // 手机号输入
  onPhoneInput(e) {
    this.setData({
      phone: e.detail.value
    })
  },

  // 验证码输入
  onCodeInput(e) {
    this.setData({
      code: e.detail.value
    })
  },

  // 发送验证码
  async onSendCode() {
    const { phone, canSendCode } = this.data
    
    if (!canSendCode) return
    
    if (!phone.trim()) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return
    }
    
    if (!validatePhone(phone)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      })
      return
    }

    try {
      showLoading('发送中...')
      
      const res = await post('/auth/send-code', { phone })
      
      wx.showToast({
        title: '验证码已发送',
        icon: 'success'
      })
      
      // 开发环境显示验证码
      if (res.data && res.data.code) {
        wx.showModal({
          title: '开发环境提示',
          content: `验证码: ${res.data.code}`,
          showCancel: false
        })
      }
      
      // 开始倒计时
      this.startCountdown()
      
    } catch (error) {
      console.error('发送验证码失败:', error)
      wx.showToast({
        title: error.message || '发送失败',
        icon: 'none'
      })
    } finally {
      hideLoading()
    }
  },

  // 开始倒计时
  startCountdown() {
    let countdown = 60
    this.setData({
      countdown,
      canSendCode: false
    })
    
    const timer = setInterval(() => {
      countdown--
      this.setData({ countdown })
      
      if (countdown <= 0) {
        clearInterval(timer)
        this.setData({
          countdown: 0,
          canSendCode: true
        })
      }
    }, 1000)
  },

  // 手机号登录
  async onPhoneLogin() {
    const { phone, code, agreementChecked } = this.data
    
    if (!agreementChecked) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }
    
    if (!phone.trim()) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return
    }
    
    if (!validatePhone(phone)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      })
      return
    }
    
    if (!code.trim()) {
      wx.showToast({
        title: '请输入验证码',
        icon: 'none'
      })
      return
    }

    try {
      this.setData({ loading: true })
      showLoading('登录中...')
      
      const res = await post('/auth/login', {
        phone: phone.trim(),
        code: code.trim()
      })
      
      // 保存登录信息
      const app = getApp()
      app.setUserInfo(res.data.userInfo)
      app.setToken(res.data.token)
      
      wx.showToast({
        title: '登录成功',
        icon: 'success'
      })
      
      // 跳转页面
      setTimeout(() => {
        if (this.redirectUrl) {
          wx.redirectTo({
            url: this.redirectUrl
          })
        } else {
          wx.switchTab({
            url: '/pages/index/index'
          })
        }
      }, 1500)
      
    } catch (error) {
      console.error('登录失败:', error)
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
      hideLoading()
    }
  },

  // 微信登录
  async onWechatLogin() {
    const { agreementChecked } = this.data
    
    if (!agreementChecked) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    try {
      this.setData({ loading: true })
      showLoading('登录中...')
      
      // 获取微信登录凭证
      const loginRes = await new Promise((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject
        })
      })
      
      if (!loginRes.code) {
        throw new Error('获取微信登录凭证失败')
      }
      
      // 获取用户信息
      const userInfoRes = await new Promise((resolve, reject) => {
        wx.getUserProfile({
          desc: '用于完善用户资料',
          success: resolve,
          fail: reject
        })
      })
      
      // 调用后端登录接口
      const res = await post('/auth/wechat-login', {
        code: loginRes.code,
        userInfo: userInfoRes.userInfo
      })
      
      // 保存登录信息
      const app = getApp()
      app.setUserInfo(res.data.userInfo)
      app.setToken(res.data.token)
      
      wx.showToast({
        title: '登录成功',
        icon: 'success'
      })
      
      // 跳转页面
      setTimeout(() => {
        if (this.redirectUrl) {
          wx.redirectTo({
            url: this.redirectUrl
          })
        } else {
          wx.switchTab({
            url: '/pages/index/index'
          })
        }
      }, 1500)
      
    } catch (error) {
      console.error('微信登录失败:', error)
      
      let message = '登录失败'
      if (error.errMsg && error.errMsg.includes('getUserProfile:fail auth deny')) {
        message = '需要授权才能登录'
      } else if (error.message) {
        message = error.message
      }
      
      wx.showToast({
        title: message,
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
      hideLoading()
    }
  },

  // 切换协议同意状态
  onToggleAgreement() {
    this.setData({
      agreementChecked: !this.data.agreementChecked
    })
  },

  // 查看用户协议
  onViewAgreement() {
    wx.showModal({
      title: '用户协议',
      content: '这里是用户协议内容...',
      showCancel: false
    })
  },

  // 查看隐私政策
  onViewPrivacy() {
    wx.showModal({
      title: '隐私政策',
      content: '这里是隐私政策内容...',
      showCancel: false
    })
  },

  // 返回上一页
  onBack() {
    if (getCurrentPages().length > 1) {
      wx.navigateBack()
    } else {
      wx.switchTab({
        url: '/pages/index/index'
      })
    }
  }
})
