// utils/request.js - 网络请求工具

const app = getApp()

/**
 * 网络请求配置
 */
const config = {
  baseUrl: 'http://localhost:8080/api',
  timeout: 10000,
  retryCount: 3,
  retryDelay: 1000
}

/**
 * 请求拦截器
 */
function requestInterceptor(options) {
  // 添加loading
  if (options.loading !== false) {
    wx.showLoading({
      title: options.loadingText || '加载中...',
      mask: true
    })
  }

  // 添加token
  const userInfo = app.globalData.userInfo
  if (userInfo && userInfo.token) {
    options.header = {
      ...options.header,
      'Authorization': `Bearer ${userInfo.token}`
    }
  }

  // 添加公共header
  options.header = {
    'Content-Type': 'application/json',
    ...options.header
  }

  return options
}

/**
 * 响应拦截器
 */
function responseInterceptor(res, options) {
  // 隐藏loading
  if (options.loading !== false) {
    wx.hideLoading()
  }

  // 处理HTTP状态码
  if (res.statusCode !== 200) {
    throw new Error(`HTTP ${res.statusCode}: ${res.data?.message || '网络错误'}`)
  }

  // 处理业务状态码
  const data = res.data
  if (data.code !== 200) {
    // 特殊状态码处理
    switch (data.code) {
      case 401:
        // 未授权，清除登录信息
        app.logout()
        wx.showModal({
          title: '提示',
          content: '登录已过期，请重新登录',
          showCancel: false,
          success: () => {
            wx.switchTab({
              url: '/pages/profile/profile'
            })
          }
        })
        break
      case 403:
        wx.showToast({
          title: '权限不足',
          icon: 'none'
        })
        break
      default:
        if (options.showError !== false) {
          wx.showToast({
            title: data.message || '请求失败',
            icon: 'none'
          })
        }
    }
    throw new Error(data.message || '请求失败')
  }

  return data
}

/**
 * 基础请求方法
 */
function request(options) {
  return new Promise((resolve, reject) => {
    let retryCount = 0

    function makeRequest() {
      // 请求拦截
      const requestOptions = requestInterceptor({
        url: config.baseUrl + options.url,
        method: options.method || 'GET',
        data: options.data,
        header: options.header || {},
        timeout: options.timeout || config.timeout,
        ...options
      })

      wx.request({
        ...requestOptions,
        success: (res) => {
          try {
            const data = responseInterceptor(res, options)
            resolve(data)
          } catch (error) {
            handleError(error)
          }
        },
        fail: (error) => {
          handleError(error)
        }
      })
    }

    function handleError(error) {
      console.error('请求失败:', error)

      // 网络错误重试
      if (retryCount < config.retryCount && 
          (error.errMsg?.includes('timeout') || error.errMsg?.includes('fail'))) {
        retryCount++
        console.log(`第${retryCount}次重试...`)
        
        setTimeout(() => {
          makeRequest()
        }, config.retryDelay * retryCount)
        return
      }

      // 隐藏loading
      if (options.loading !== false) {
        wx.hideLoading()
      }

      // 显示错误提示
      if (options.showError !== false) {
        wx.showToast({
          title: error.message || '网络错误',
          icon: 'none'
        })
      }

      reject(error)
    }

    makeRequest()
  })
}

/**
 * GET请求
 */
function get(url, data = {}, options = {}) {
  // 将data转换为查询参数
  if (Object.keys(data).length > 0) {
    const params = Object.keys(data)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`)
      .join('&')
    url += (url.includes('?') ? '&' : '?') + params
  }

  return request({
    url,
    method: 'GET',
    ...options
  })
}

/**
 * POST请求
 */
function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

/**
 * PUT请求
 */
function put(url, data = {}, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

/**
 * DELETE请求
 */
function del(url, data = {}, options = {}) {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  })
}

/**
 * 文件上传
 */
function upload(url, filePath, formData = {}, options = {}) {
  return new Promise((resolve, reject) => {
    // 添加loading
    if (options.loading !== false) {
      wx.showLoading({
        title: options.loadingText || '上传中...',
        mask: true
      })
    }

    // 添加token
    const header = { ...options.header }
    const userInfo = app.globalData.userInfo
    if (userInfo && userInfo.token) {
      header['Authorization'] = `Bearer ${userInfo.token}`
    }

    const uploadTask = wx.uploadFile({
      url: config.baseUrl + url,
      filePath: filePath,
      name: options.name || 'file',
      formData: formData,
      header: header,
      success: (res) => {
        try {
          const data = JSON.parse(res.data)
          if (data.code === 200) {
            resolve(data)
          } else {
            throw new Error(data.message || '上传失败')
          }
        } catch (error) {
          reject(new Error('上传失败'))
        }
      },
      fail: (error) => {
        console.error('上传失败:', error)
        if (options.showError !== false) {
          wx.showToast({
            title: '上传失败',
            icon: 'none'
          })
        }
        reject(error)
      },
      complete: () => {
        if (options.loading !== false) {
          wx.hideLoading()
        }
      }
    })

    // 监听上传进度
    if (options.onProgress) {
      uploadTask.onProgressUpdate(options.onProgress)
    }

    return uploadTask
  })
}

/**
 * 下载文件
 */
function download(url, options = {}) {
  return new Promise((resolve, reject) => {
    if (options.loading !== false) {
      wx.showLoading({
        title: options.loadingText || '下载中...',
        mask: true
      })
    }

    const downloadTask = wx.downloadFile({
      url: config.baseUrl + url,
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res)
        } else {
          reject(new Error('下载失败'))
        }
      },
      fail: (error) => {
        console.error('下载失败:', error)
        if (options.showError !== false) {
          wx.showToast({
            title: '下载失败',
            icon: 'none'
          })
        }
        reject(error)
      },
      complete: () => {
        if (options.loading !== false) {
          wx.hideLoading()
        }
      }
    })

    // 监听下载进度
    if (options.onProgress) {
      downloadTask.onProgressUpdate(options.onProgress)
    }

    return downloadTask
  })
}

module.exports = {
  config,
  request,
  get,
  post,
  put,
  delete: del,
  upload,
  download
}
