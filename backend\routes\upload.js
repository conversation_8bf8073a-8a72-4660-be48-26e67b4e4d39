const express = require('express')
const multer = require('multer')
const path = require('path')
const fs = require('fs')
const { authenticateToken } = require('../middleware/auth')
const router = express.Router()

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../uploads')
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true })
}

// 配置multer
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadDir)
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
    const ext = path.extname(file.originalname)
    cb(null, file.fieldname + '-' + uniqueSuffix + ext)
  }
})

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 检查文件类型
  const allowedTypes = /jpeg|jpg|png|gif|webp/
  const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase())
  const mimetype = allowedTypes.test(file.mimetype)

  if (mimetype && extname) {
    return cb(null, true)
  } else {
    cb(new Error('只允许上传图片文件 (jpeg, jpg, png, gif, webp)'))
  }
}

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024 // 5MB
  }
})

// 单文件上传
router.post('/', authenticateToken, upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '没有上传文件'
      })
    }

    const fileUrl = `/uploads/${req.file.filename}`

    res.json({
      code: 0,
      message: '上传成功',
      data: {
        filename: req.file.filename,
        originalname: req.file.originalname,
        size: req.file.size,
        url: fileUrl,
        full_url: `${req.protocol}://${req.get('host')}${fileUrl}`
      }
    })
  } catch (error) {
    console.error('文件上传错误:', error)
    res.status(500).json({
      code: 500,
      message: '文件上传失败'
    })
  }
})

// 多文件上传
router.post('/multiple', authenticateToken, upload.array('files', 10), (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '没有上传文件'
      })
    }

    const files = req.files.map(file => ({
      filename: file.filename,
      originalname: file.originalname,
      size: file.size,
      url: `/uploads/${file.filename}`,
      full_url: `${req.protocol}://${req.get('host')}/uploads/${file.filename}`
    }))

    res.json({
      code: 0,
      message: '上传成功',
      data: files
    })
  } catch (error) {
    console.error('多文件上传错误:', error)
    res.status(500).json({
      code: 500,
      message: '文件上传失败'
    })
  }
})

// 删除文件
router.delete('/:filename', authenticateToken, (req, res) => {
  try {
    const { filename } = req.params
    const filePath = path.join(uploadDir, filename)

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        code: 404,
        message: '文件不存在'
      })
    }

    // 删除文件
    fs.unlinkSync(filePath)

    res.json({
      code: 0,
      message: '删除成功'
    })
  } catch (error) {
    console.error('删除文件错误:', error)
    res.status(500).json({
      code: 500,
      message: '删除文件失败'
    })
  }
})

// 错误处理中间件
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        code: 400,
        message: '文件大小超出限制'
      })
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        code: 400,
        message: '文件数量超出限制'
      })
    }
  }

  res.status(400).json({
    code: 400,
    message: error.message || '文件上传失败'
  })
})

module.exports = router
