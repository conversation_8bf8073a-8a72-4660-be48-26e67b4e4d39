<!--components/star-rating/star-rating.wxml-->
<view class="star-rating star-rating-{{size}}">
  <view class="stars-container">
    <view 
      class="star-item {{star.type}}"
      wx:for="{{stars}}"
      wx:key="index"
      wx:for-item="star"
      bindtap="onStarTap"
      data-index="{{star.index}}"
      animation="{{star.animation}}"
    >
      <!-- 空星星背景 -->
      <view class="star-bg">⭐</view>
      
      <!-- 填充的星星 -->
      <view class="star-fill {{star.type}}" wx:if="{{star.type !== 'empty'}}">
        ⭐
      </view>
      
      <!-- 半星遮罩 -->
      <view class="star-half-mask" wx:if="{{star.type === 'half'}}"></view>
    </view>
  </view>
  
  <!-- 评分文字 -->
  <view class="rating-info" wx:if="{{showText}}">
    <text class="rating-number">{{currentRating.toFixed(1)}}</text>
    <text class="rating-text">{{getRatingText(currentRating)}}</text>
  </view>
</view>
