const express = require('express')
const { query, transaction } = require('../config/database')
const { authenticateToken } = require('../middleware/auth')
const router = express.Router()

// 生成订单号
function generateOrderNo() {
  const now = new Date()
  const year = now.getFullYear().toString().slice(-2)
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  const hour = now.getHours().toString().padStart(2, '0')
  const minute = now.getMinutes().toString().padStart(2, '0')
  const second = now.getSeconds().toString().padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  
  return `${year}${month}${day}${hour}${minute}${second}${random}`
}

// 创建订单
router.post('/', authenticateToken, async (req, res) => {
  try {
    const {
      items,
      total_amount,
      remark,
      contact_name,
      contact_phone,
      delivery_address
    } = req.body

    // 验证必填字段
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '订单商品不能为空'
      })
    }

    if (!total_amount || total_amount <= 0) {
      return res.status(400).json({
        code: 400,
        message: '订单金额不正确'
      })
    }

    if (!contact_name || !contact_phone) {
      return res.status(400).json({
        code: 400,
        message: '联系人信息不能为空'
      })
    }

    // 使用事务创建订单
    const result = await transaction(async (connection) => {
      // 生成订单号
      const orderNo = generateOrderNo()

      // 验证菜品并计算总价
      let calculatedTotal = 0
      const validItems = []

      for (const item of items) {
        const [dish] = await connection.execute(
          'SELECT id, name, price, status FROM dishes WHERE id = ?',
          [item.dish_id]
        )

        if (!dish || dish.status !== 1) {
          throw new Error(`菜品 ${item.dish_id} 不存在或已下架`)
        }

        if (!item.quantity || item.quantity <= 0) {
          throw new Error('商品数量必须大于0')
        }

        const subtotal = dish.price * item.quantity
        calculatedTotal += subtotal

        validItems.push({
          dish_id: dish.id,
          dish_name: dish.name,
          dish_price: dish.price,
          quantity: item.quantity,
          subtotal
        })
      }

      // 验证总价（允许小数点误差）
      if (Math.abs(calculatedTotal - total_amount) > 0.01) {
        throw new Error('订单金额计算错误')
      }

      // 创建订单
      const [orderResult] = await connection.execute(`
        INSERT INTO orders (
          order_no, user_id, total_amount, status, remark, 
          contact_name, contact_phone, delivery_address
        ) VALUES (?, ?, ?, 'pending', ?, ?, ?, ?)
      `, [
        orderNo,
        req.user.id,
        total_amount,
        remark || null,
        contact_name,
        contact_phone,
        delivery_address || null
      ])

      const orderId = orderResult.insertId

      // 创建订单详情
      for (const item of validItems) {
        await connection.execute(`
          INSERT INTO order_items (
            order_id, dish_id, dish_name, dish_price, quantity, subtotal
          ) VALUES (?, ?, ?, ?, ?, ?)
        `, [
          orderId,
          item.dish_id,
          item.dish_name,
          item.dish_price,
          item.quantity,
          item.subtotal
        ])

        // 更新菜品销量
        await connection.execute(
          'UPDATE dishes SET sales_count = sales_count + ? WHERE id = ?',
          [item.quantity, item.dish_id]
        )
      }

      return { orderId, orderNo }
    })

    res.json({
      code: 0,
      message: '订单创建成功',
      data: {
        order_id: result.orderId,
        order_no: result.orderNo
      }
    })
  } catch (error) {
    console.error('创建订单错误:', error)
    res.status(500).json({
      code: 500,
      message: error.message || '创建订单失败'
    })
  }
})

// 获取用户订单列表
router.get('/', authenticateToken, async (req, res) => {
  try {
    const {
      status,
      page = 1,
      limit = 10
    } = req.query

    let sql = `
      SELECT o.*, 
      (SELECT COUNT(*) FROM order_items oi WHERE oi.order_id = o.id) as item_count
      FROM orders o 
      WHERE o.user_id = ?
    `
    const params = [req.user.id]

    // 状态筛选
    if (status) {
      sql += ' AND o.status = ?'
      params.push(status)
    }

    sql += ' ORDER BY o.created_at DESC'

    // 分页
    const offset = (page - 1) * limit
    sql += ' LIMIT ? OFFSET ?'
    params.push(parseInt(limit), parseInt(offset))

    const orders = await query(sql, params)

    // 获取订单详情
    for (const order of orders) {
      const items = await query(`
        SELECT oi.*, d.image as dish_image
        FROM order_items oi
        LEFT JOIN dishes d ON oi.dish_id = d.id
        WHERE oi.order_id = ?
        ORDER BY oi.id
      `, [order.id])
      
      order.items = items
    }

    // 获取总数
    let countSql = 'SELECT COUNT(*) as total FROM orders WHERE user_id = ?'
    const countParams = [req.user.id]

    if (status) {
      countSql += ' AND status = ?'
      countParams.push(status)
    }

    const [{ total }] = await query(countSql, countParams)

    res.json({
      code: 0,
      message: '获取成功',
      data: {
        list: orders,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    console.error('获取订单列表错误:', error)
    res.status(500).json({
      code: 500,
      message: '获取订单列表失败'
    })
  }
})

// 获取订单详情
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params

    const orders = await query(`
      SELECT * FROM orders 
      WHERE id = ? AND user_id = ?
    `, [id, req.user.id])

    if (orders.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '订单不存在'
      })
    }

    const order = orders[0]

    // 获取订单详情
    const items = await query(`
      SELECT oi.*, d.image as dish_image
      FROM order_items oi
      LEFT JOIN dishes d ON oi.dish_id = d.id
      WHERE oi.order_id = ?
      ORDER BY oi.id
    `, [order.id])

    order.items = items

    res.json({
      code: 0,
      message: '获取成功',
      data: order
    })
  } catch (error) {
    console.error('获取订单详情错误:', error)
    res.status(500).json({
      code: 500,
      message: '获取订单详情失败'
    })
  }
})

// 取消订单
router.put('/:id/cancel', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params

    const orders = await query(`
      SELECT * FROM orders 
      WHERE id = ? AND user_id = ?
    `, [id, req.user.id])

    if (orders.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '订单不存在'
      })
    }

    const order = orders[0]

    if (order.status !== 'pending') {
      return res.status(400).json({
        code: 400,
        message: '只能取消待确认的订单'
      })
    }

    await query(
      'UPDATE orders SET status = "cancelled" WHERE id = ?',
      [id]
    )

    res.json({
      code: 0,
      message: '订单取消成功'
    })
  } catch (error) {
    console.error('取消订单错误:', error)
    res.status(500).json({
      code: 500,
      message: '取消订单失败'
    })
  }
})

// 获取订单统计
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const stats = await query(`
      SELECT 
        status,
        COUNT(*) as count
      FROM orders 
      WHERE user_id = ?
      GROUP BY status
    `, [req.user.id])

    const result = {
      pending: 0,
      confirmed: 0,
      preparing: 0,
      completed: 0,
      cancelled: 0
    }

    stats.forEach(stat => {
      result[stat.status] = stat.count
    })

    res.json({
      code: 0,
      message: '获取成功',
      data: result
    })
  } catch (error) {
    console.error('获取订单统计错误:', error)
    res.status(500).json({
      code: 500,
      message: '获取订单统计失败'
    })
  }
})

module.exports = router
