// utils/request.js

// 请求配置
const config = {
  baseUrl: 'http://localhost:3000/api',
  timeout: 10000,
  retryCount: 3,
  retryDelay: 1000
}

// 请求队列管理
const requestQueue = new Map()
let requestId = 0

// 生成请求ID
const generateRequestId = () => {
  return ++requestId
}

// 请求拦截器
const request = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    const app = getApp()
    const reqId = generateRequestId()
    
    // 检查网络状态
    if (!app.isNetworkConnected()) {
      reject(new Error('网络连接异常，请检查网络设置'))
      return
    }
    
    // 获取token
    const token = app.getToken()
    
    // 设置默认headers
    const headers = {
      'Content-Type': 'application/json',
      'X-Request-ID': reqId.toString(),
      ...options.headers
    }
    
    // 添加token
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }
    
    // 请求参数
    const requestOptions = {
      url: config.baseUrl + url,
      method: options.method || 'GET',
      data: options.data,
      header: headers,
      timeout: options.timeout || config.timeout,
      success: (res) => {
        // 移除请求队列
        requestQueue.delete(reqId)
        
        console.log(`请求成功 [${reqId}]:`, url, res.statusCode)
        
        if (res.statusCode === 200) {
          if (res.data.code === 0) {
            resolve(res.data)
          } else {
            const error = new Error(res.data.message || '请求失败')
            error.code = res.data.code
            reject(error)
          }
        } else if (res.statusCode === 401) {
          // token过期，清除登录信息
          app.clearUserInfo()
          wx.showToast({
            title: '登录已过期，请重新登录',
            icon: 'none'
          })
          
          // 跳转到登录页
          setTimeout(() => {
            wx.navigateTo({
              url: '/pages/login/login'
            })
          }, 1500)
          
          reject(new Error('登录已过期'))
        } else if (res.statusCode === 403) {
          reject(new Error('没有权限访问'))
        } else if (res.statusCode === 404) {
          reject(new Error('请求的资源不存在'))
        } else if (res.statusCode >= 500) {
          reject(new Error('服务器内部错误'))
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`))
        }
      },
      fail: (error) => {
        // 移除请求队列
        requestQueue.delete(reqId)
        
        console.error(`请求失败 [${reqId}]:`, url, error)
        
        let errorMessage = '网络请求失败'
        
        if (error.errMsg) {
          if (error.errMsg.includes('timeout')) {
            errorMessage = '请求超时，请检查网络连接'
          } else if (error.errMsg.includes('fail')) {
            errorMessage = '网络连接失败'
          }
        }
        
        // 自动重试机制
        if (options.retryCount > 0) {
          console.log(`请求重试 [${reqId}]:`, url, `剩余重试次数: ${options.retryCount}`)
          
          setTimeout(() => {
            request(url, {
              ...options,
              retryCount: options.retryCount - 1
            }).then(resolve).catch(reject)
          }, options.retryDelay || config.retryDelay)
        } else {
          reject(new Error(errorMessage))
        }
      }
    }
    
    // 添加到请求队列
    requestQueue.set(reqId, requestOptions)
    
    console.log(`发起请求 [${reqId}]:`, requestOptions.method, url)
    
    // 发起请求
    wx.request(requestOptions)
  })
}

// 带重试的请求
const requestWithRetry = (url, options = {}) => {
  return request(url, {
    ...options,
    retryCount: options.retryCount || config.retryCount,
    retryDelay: options.retryDelay || config.retryDelay
  })
}

// GET请求
const get = (url, data, options = {}) => {
  let queryString = ''
  if (data && Object.keys(data).length > 0) {
    queryString = '?' + Object.keys(data)
      .filter(key => data[key] !== undefined && data[key] !== null)
      .map(key => `${key}=${encodeURIComponent(data[key])}`)
      .join('&')
  }
  
  return requestWithRetry(url + queryString, {
    method: 'GET',
    ...options
  })
}

// POST请求
const post = (url, data, options = {}) => {
  return requestWithRetry(url, {
    method: 'POST',
    data,
    ...options
  })
}

// PUT请求
const put = (url, data, options = {}) => {
  return requestWithRetry(url, {
    method: 'PUT',
    data,
    ...options
  })
}

// DELETE请求
const del = (url, options = {}) => {
  return requestWithRetry(url, {
    method: 'DELETE',
    ...options
  })
}

// 文件上传
const uploadFile = (url, filePath, name = 'file', formData = {}) => {
  return new Promise((resolve, reject) => {
    const app = getApp()
    const token = app.getToken()
    
    const headers = {}
    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }
    
    wx.uploadFile({
      url: config.baseUrl + url,
      filePath,
      name,
      formData,
      header: headers,
      success: (res) => {
        try {
          const data = JSON.parse(res.data)
          if (data.code === 0) {
            resolve(data)
          } else {
            reject(new Error(data.message || '上传失败'))
          }
        } catch (error) {
          reject(new Error('响应数据格式错误'))
        }
      },
      fail: (error) => {
        console.error('文件上传失败:', error)
        reject(new Error('文件上传失败'))
      }
    })
  })
}

// 取消所有请求
const cancelAllRequests = () => {
  requestQueue.clear()
  console.log('已取消所有请求')
}

// 获取请求队列状态
const getRequestQueueStatus = () => {
  return {
    count: requestQueue.size,
    requests: Array.from(requestQueue.keys())
  }
}

// 设置基础URL
const setBaseUrl = (url) => {
  config.baseUrl = url
}

// 设置超时时间
const setRequestTimeout = (timeout) => {
  config.timeout = timeout
}

module.exports = {
  request,
  requestWithRetry,
  get,
  post,
  put,
  delete: del,
  uploadFile,
  cancelAllRequests,
  getRequestQueueStatus,
  setBaseUrl,
  setTimeout: setRequestTimeout,
  config
}
