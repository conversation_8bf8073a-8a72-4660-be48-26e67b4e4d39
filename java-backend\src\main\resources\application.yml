server:
  port: 8080
  servlet:
    context-path: /api
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB

spring:
  application:
    name: ordering-system-backend
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: OrderingSystemHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 50MB

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    auto-mapping-behavior: full
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:mapper/*.xml
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      id-type: auto

# 图片上传配置
file:
  upload:
    # 上传路径
    path: ./uploads/
    # 访问路径前缀
    access-path: /uploads/**
    # 允许的图片格式
    allowed-types: jpg,jpeg,png,gif,webp
    # 最大文件大小 (10MB)
    max-size: 10485760
    # 图片压缩配置
    compress:
      # 是否启用压缩
      enabled: true
      # 压缩质量 (0.1-1.0)
      quality: 0.8
      # 最大宽度
      max-width: 1920
      # 最大高度
      max-height: 1080
      # 缩略图尺寸
      thumbnail:
        width: 300
        height: 300

# JWT配置
jwt:
  secret: ordering-system-jwt-secret-key-2024
  expiration: 86400000 # 24小时
  header: Authorization
  prefix: Bearer

# 日志配置
logging:
  level:
    com.ordering: debug
    org.springframework.web: debug
    org.hibernate.SQL: debug
    org.hibernate.type.descriptor.sql.BasicBinder: trace
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/ordering-system.log
    max-size: 100MB
    max-history: 30

# 跨域配置
cors:
  allowed-origins: 
    - http://localhost:3000
    - https://servicewechat.com
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  allow-credentials: true
  max-age: 3600
