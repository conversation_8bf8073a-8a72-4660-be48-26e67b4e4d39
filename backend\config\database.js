const mysql = require('mysql2/promise')
require('dotenv').config()

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '123456',
  database: process.env.DB_NAME || 'ordering_system',
  charset: 'utf8mb4',
  timezone: '+08:00',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
}

// 创建连接池
const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
})

// 测试数据库连接
async function testConnection() {
  try {
    const connection = await pool.getConnection()
    console.log('✅ 数据库连接成功')
    connection.release()
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message)
    process.exit(1)
  }
}

// 执行查询
async function query(sql, params = []) {
  try {
    const [rows] = await pool.execute(sql, params)
    return rows
  } catch (error) {
    console.error('数据库查询错误:', error)
    throw error
  }
}

// 执行事务
async function transaction(callback) {
  const connection = await pool.getConnection()
  await connection.beginTransaction()
  
  try {
    const result = await callback(connection)
    await connection.commit()
    return result
  } catch (error) {
    await connection.rollback()
    throw error
  } finally {
    connection.release()
  }
}

// 初始化数据库
async function initDatabase() {
  try {
    // 创建数据库（如果不存在）
    const createDbSql = `CREATE DATABASE IF NOT EXISTS ${dbConfig.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`
    const tempPool = mysql.createPool({
      ...dbConfig,
      database: undefined
    })
    
    await tempPool.execute(createDbSql)
    await tempPool.end()
    
    console.log('✅ 数据库初始化完成')
    
    // 测试连接
    await testConnection()
    
    // 创建表
    await createTables()
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error)
    throw error
  }
}

// 创建表
async function createTables() {
  const tables = [
    // 用户表
    `CREATE TABLE IF NOT EXISTS users (
      id INT PRIMARY KEY AUTO_INCREMENT,
      phone VARCHAR(11) UNIQUE,
      nickname VARCHAR(50),
      avatar VARCHAR(255),
      openid VARCHAR(100) UNIQUE,
      unionid VARCHAR(100),
      gender TINYINT DEFAULT 0 COMMENT '0:未知 1:男 2:女',
      birthday DATE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_phone (phone),
      INDEX idx_openid (openid)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

    // 分类表
    `CREATE TABLE IF NOT EXISTS categories (
      id INT PRIMARY KEY AUTO_INCREMENT,
      name VARCHAR(50) NOT NULL,
      icon VARCHAR(255),
      sort_order INT DEFAULT 0,
      status TINYINT DEFAULT 1 COMMENT '0:禁用 1:启用',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

    // 菜品表
    `CREATE TABLE IF NOT EXISTS dishes (
      id INT PRIMARY KEY AUTO_INCREMENT,
      category_id INT NOT NULL,
      name VARCHAR(100) NOT NULL,
      description TEXT,
      price DECIMAL(10,2) NOT NULL,
      image VARCHAR(255),
      is_hot TINYINT DEFAULT 0 COMMENT '是否热门',
      is_new TINYINT DEFAULT 0 COMMENT '是否新品',
      status TINYINT DEFAULT 1 COMMENT '0:下架 1:上架',
      sort_order INT DEFAULT 0,
      sales_count INT DEFAULT 0 COMMENT '销量',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
      INDEX idx_category (category_id),
      INDEX idx_status (status),
      INDEX idx_hot (is_hot),
      INDEX idx_new (is_new)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

    // 订单表
    `CREATE TABLE IF NOT EXISTS orders (
      id INT PRIMARY KEY AUTO_INCREMENT,
      order_no VARCHAR(32) UNIQUE NOT NULL,
      user_id INT NOT NULL,
      total_amount DECIMAL(10,2) NOT NULL,
      status VARCHAR(20) DEFAULT 'pending' COMMENT 'pending:待确认 confirmed:已确认 preparing:制作中 completed:已完成 cancelled:已取消',
      remark TEXT COMMENT '备注',
      contact_name VARCHAR(50),
      contact_phone VARCHAR(11),
      delivery_address TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
      INDEX idx_user (user_id),
      INDEX idx_status (status),
      INDEX idx_order_no (order_no)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

    // 订单详情表
    `CREATE TABLE IF NOT EXISTS order_items (
      id INT PRIMARY KEY AUTO_INCREMENT,
      order_id INT NOT NULL,
      dish_id INT NOT NULL,
      dish_name VARCHAR(100) NOT NULL,
      dish_price DECIMAL(10,2) NOT NULL,
      quantity INT NOT NULL,
      subtotal DECIMAL(10,2) NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
      FOREIGN KEY (dish_id) REFERENCES dishes(id) ON DELETE CASCADE,
      INDEX idx_order (order_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

    // 轮播图表
    `CREATE TABLE IF NOT EXISTS banners (
      id INT PRIMARY KEY AUTO_INCREMENT,
      title VARCHAR(100),
      image VARCHAR(255) NOT NULL,
      link_type VARCHAR(20) COMMENT 'dish:菜品 category:分类 url:链接',
      link_id INT COMMENT '关联ID',
      link_url VARCHAR(255) COMMENT '链接地址',
      sort_order INT DEFAULT 0,
      status TINYINT DEFAULT 1 COMMENT '0:禁用 1:启用',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`
  ]

  for (const sql of tables) {
    await query(sql)
  }
  
  console.log('✅ 数据表创建完成')
}

module.exports = {
  pool,
  query,
  transaction,
  initDatabase
}
