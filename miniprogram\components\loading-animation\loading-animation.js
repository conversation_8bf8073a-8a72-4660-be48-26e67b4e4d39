// components/loading-animation/loading-animation.js
Component({
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    type: {
      type: String,
      value: 'food' // food, dots, pulse, wave
    },
    text: {
      type: String,
      value: '加载中...'
    }
  },

  data: {
    animationData: {}
  },

  observers: {
    'show': function(show) {
      if (show) {
        this.startAnimation()
      } else {
        this.stopAnimation()
      }
    }
  },

  methods: {
    startAnimation() {
      if (this.data.type === 'food') {
        this.startFoodAnimation()
      } else if (this.data.type === 'pulse') {
        this.startPulseAnimation()
      }
    },

    stopAnimation() {
      this.setData({
        animationData: {}
      })
    },

    startFoodAnimation() {
      const animation = wx.createAnimation({
        duration: 1000,
        timingFunction: 'ease-in-out',
        iterationCount: 'infinite'
      })

      const animate = () => {
        animation.rotate(360).step()
        this.setData({
          animationData: animation.export()
        })
      }

      animate()
      this.animationTimer = setInterval(animate, 1000)
    },

    startPulseAnimation() {
      const animation = wx.createAnimation({
        duration: 800,
        timingFunction: 'ease-in-out',
        iterationCount: 'infinite'
      })

      const animate = () => {
        animation.scale(1.2).step({ duration: 400 })
        animation.scale(1).step({ duration: 400 })
        this.setData({
          animationData: animation.export()
        })
      }

      animate()
      this.animationTimer = setInterval(animate, 800)
    }
  },

  lifetimes: {
    detached() {
      if (this.animationTimer) {
        clearInterval(this.animationTimer)
      }
    }
  }
})
