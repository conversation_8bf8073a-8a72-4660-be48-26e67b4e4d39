const express = require('express')
const bcrypt = require('bcryptjs')
const { query } = require('../config/database')
const { generateToken } = require('../middleware/auth')
const router = express.Router()

// 模拟验证码存储（生产环境应使用Redis）
const verificationCodes = new Map()

// 发送验证码
router.post('/send-code', async (req, res) => {
  try {
    const { phone } = req.body

    if (!phone) {
      return res.status(400).json({
        code: 400,
        message: '手机号不能为空'
      })
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(phone)) {
      return res.status(400).json({
        code: 400,
        message: '手机号格式不正确'
      })
    }

    // 生成6位验证码
    const code = Math.random().toString().slice(-6)
    
    // 存储验证码（5分钟有效期）
    verificationCodes.set(phone, {
      code,
      expires: Date.now() + 5 * 60 * 1000
    })

    // 这里应该调用短信服务发送验证码
    // 开发环境下直接返回验证码（生产环境删除）
    console.log(`验证码发送到 ${phone}: ${code}`)

    res.json({
      code: 0,
      message: '验证码发送成功',
      data: {
        // 开发环境返回验证码，生产环境删除
        code: process.env.NODE_ENV === 'development' ? code : undefined
      }
    })
  } catch (error) {
    console.error('发送验证码错误:', error)
    res.status(500).json({
      code: 500,
      message: '发送验证码失败'
    })
  }
})

// 手机号登录
router.post('/login', async (req, res) => {
  try {
    const { phone, code } = req.body

    if (!phone || !code) {
      return res.status(400).json({
        code: 400,
        message: '手机号和验证码不能为空'
      })
    }

    // 验证验证码
    const storedCode = verificationCodes.get(phone)
    if (!storedCode) {
      return res.status(400).json({
        code: 400,
        message: '验证码不存在或已过期'
      })
    }

    if (storedCode.expires < Date.now()) {
      verificationCodes.delete(phone)
      return res.status(400).json({
        code: 400,
        message: '验证码已过期'
      })
    }

    if (storedCode.code !== code) {
      return res.status(400).json({
        code: 400,
        message: '验证码错误'
      })
    }

    // 删除已使用的验证码
    verificationCodes.delete(phone)

    // 查找或创建用户
    let users = await query(
      'SELECT * FROM users WHERE phone = ?',
      [phone]
    )

    let user
    if (users.length === 0) {
      // 创建新用户
      const result = await query(
        'INSERT INTO users (phone, nickname) VALUES (?, ?)',
        [phone, `用户${phone.slice(-4)}`]
      )
      
      user = {
        id: result.insertId,
        phone,
        nickname: `用户${phone.slice(-4)}`,
        avatar: null,
        openid: null
      }
    } else {
      user = users[0]
    }

    // 生成JWT token
    const token = generateToken(user.id)

    res.json({
      code: 0,
      message: '登录成功',
      data: {
        token,
        userInfo: {
          id: user.id,
          phone: user.phone,
          nickname: user.nickname,
          avatar: user.avatar
        }
      }
    })
  } catch (error) {
    console.error('登录错误:', error)
    res.status(500).json({
      code: 500,
      message: '登录失败'
    })
  }
})

// 微信登录
router.post('/wechat-login', async (req, res) => {
  try {
    const { code, userInfo } = req.body

    if (!code) {
      return res.status(400).json({
        code: 400,
        message: '微信授权码不能为空'
      })
    }

    // 这里应该调用微信API获取openid和session_key
    // 由于需要微信AppID和AppSecret，这里模拟实现
    const openid = `mock_openid_${Date.now()}`

    // 查找或创建用户
    let users = await query(
      'SELECT * FROM users WHERE openid = ?',
      [openid]
    )

    let user
    if (users.length === 0) {
      // 创建新用户
      const result = await query(
        'INSERT INTO users (openid, nickname, avatar, gender) VALUES (?, ?, ?, ?)',
        [
          openid,
          userInfo?.nickName || '微信用户',
          userInfo?.avatarUrl || null,
          userInfo?.gender || 0
        ]
      )
      
      user = {
        id: result.insertId,
        openid,
        nickname: userInfo?.nickName || '微信用户',
        avatar: userInfo?.avatarUrl || null,
        phone: null
      }
    } else {
      user = users[0]
      
      // 更新用户信息
      if (userInfo) {
        await query(
          'UPDATE users SET nickname = ?, avatar = ?, gender = ? WHERE id = ?',
          [
            userInfo.nickName || user.nickname,
            userInfo.avatarUrl || user.avatar,
            userInfo.gender || user.gender,
            user.id
          ]
        )
        
        user.nickname = userInfo.nickName || user.nickname
        user.avatar = userInfo.avatarUrl || user.avatar
      }
    }

    // 生成JWT token
    const token = generateToken(user.id)

    res.json({
      code: 0,
      message: '登录成功',
      data: {
        token,
        userInfo: {
          id: user.id,
          phone: user.phone,
          nickname: user.nickname,
          avatar: user.avatar,
          openid: user.openid
        }
      }
    })
  } catch (error) {
    console.error('微信登录错误:', error)
    res.status(500).json({
      code: 500,
      message: '微信登录失败'
    })
  }
})

// 刷新token
router.post('/refresh', async (req, res) => {
  try {
    const { refreshToken } = req.body

    if (!refreshToken) {
      return res.status(400).json({
        code: 400,
        message: 'Refresh token不能为空'
      })
    }

    // 这里应该验证refresh token并生成新的access token
    // 简化实现
    res.json({
      code: 0,
      message: 'Token刷新成功',
      data: {
        token: 'new_access_token'
      }
    })
  } catch (error) {
    console.error('刷新token错误:', error)
    res.status(500).json({
      code: 500,
      message: 'Token刷新失败'
    })
  }
})

module.exports = router
