// utils/api.js - API服务

const { get, post, put, delete: del } = require('./request')

/**
 * 用户相关API
 */
const userApi = {
  // 用户登录
  login(data) {
    return post('/users/login', data)
  },

  // 获取用户信息
  getUserInfo(userId) {
    return get(`/users/${userId}`)
  },

  // 更新用户信息
  updateUserInfo(userId, data) {
    return put(`/users/${userId}`, data)
  },

  // 用户注册
  register(data) {
    return post('/users/register', data)
  }
}

/**
 * 菜品相关API
 */
const dishApi = {
  // 获取菜品列表
  getDishList(params = {}) {
    return get('/dishes', params)
  },

  // 根据分类获取菜品
  getDishesByCategory(categoryId, params = {}) {
    return get(`/dishes/category/${categoryId}`, params)
  },

  // 获取菜品详情
  getDishDetail(dishId) {
    return get(`/dishes/${dishId}`)
  },

  // 搜索菜品
  searchDishes(keyword, params = {}) {
    return get('/dishes/search', { keyword, ...params })
  },

  // 获取热门菜品
  getHotDishes(params = {}) {
    return get('/dishes/hot', params)
  },

  // 获取新品菜品
  getNewDishes(params = {}) {
    return get('/dishes/new', params)
  },

  // 获取推荐菜品
  getRecommendedDishes(params = {}) {
    return get('/dishes/recommended', params)
  },

  // 创建菜品（管理员）
  createDish(data) {
    return post('/dishes', data)
  },

  // 更新菜品（管理员）
  updateDish(dishId, data) {
    return put(`/dishes/${dishId}`, data)
  },

  // 删除菜品（管理员）
  deleteDish(dishId) {
    return del(`/dishes/${dishId}`)
  }
}

/**
 * 分类相关API
 */
const categoryApi = {
  // 获取分类列表
  getCategoryList() {
    return get('/categories')
  },

  // 获取分类详情
  getCategoryDetail(categoryId) {
    return get(`/categories/${categoryId}`)
  },

  // 创建分类（管理员）
  createCategory(data) {
    return post('/categories', data)
  },

  // 更新分类（管理员）
  updateCategory(categoryId, data) {
    return put(`/categories/${categoryId}`, data)
  },

  // 删除分类（管理员）
  deleteCategory(categoryId) {
    return del(`/categories/${categoryId}`)
  }
}

/**
 * 订单相关API
 */
const orderApi = {
  // 创建订单
  createOrder(data) {
    return post('/orders', data)
  },

  // 获取用户订单列表
  getUserOrders(userId, params = {}) {
    return get(`/orders/user/${userId}`, params)
  },

  // 获取订单详情
  getOrderDetail(orderId) {
    return get(`/orders/${orderId}`)
  },

  // 取消订单
  cancelOrder(orderId) {
    return put(`/orders/${orderId}/cancel`)
  },

  // 确认收货
  confirmOrder(orderId) {
    return put(`/orders/${orderId}/confirm`)
  },

  // 订单评价
  rateOrder(orderId, data) {
    return post(`/orders/${orderId}/rate`, data)
  },

  // 获取所有订单（管理员）
  getAllOrders(params = {}) {
    return get('/orders', params)
  },

  // 更新订单状态（管理员）
  updateOrderStatus(orderId, status) {
    return put(`/orders/${orderId}/status`, { status })
  }
}

/**
 * 购物车相关API
 */
const cartApi = {
  // 获取购物车
  getCart(userId) {
    return get(`/cart/${userId}`)
  },

  // 添加到购物车
  addToCart(data) {
    return post('/cart/add', data)
  },

  // 更新购物车商品数量
  updateCartItem(cartItemId, quantity) {
    return put(`/cart/${cartItemId}`, { quantity })
  },

  // 删除购物车商品
  removeFromCart(cartItemId) {
    return del(`/cart/${cartItemId}`)
  },

  // 清空购物车
  clearCart(userId) {
    return del(`/cart/clear/${userId}`)
  }
}

/**
 * 地址相关API
 */
const addressApi = {
  // 获取用户地址列表
  getUserAddresses(userId) {
    return get(`/addresses/user/${userId}`)
  },

  // 获取地址详情
  getAddressDetail(addressId) {
    return get(`/addresses/${addressId}`)
  },

  // 创建地址
  createAddress(data) {
    return post('/addresses', data)
  },

  // 更新地址
  updateAddress(addressId, data) {
    return put(`/addresses/${addressId}`, data)
  },

  // 删除地址
  deleteAddress(addressId) {
    return del(`/addresses/${addressId}`)
  },

  // 设置默认地址
  setDefaultAddress(addressId) {
    return put(`/addresses/${addressId}/default`)
  }
}

/**
 * 轮播图相关API
 */
const bannerApi = {
  // 获取轮播图列表
  getBannerList() {
    return get('/banners')
  },

  // 创建轮播图（管理员）
  createBanner(data) {
    return post('/banners', data)
  },

  // 更新轮播图（管理员）
  updateBanner(bannerId, data) {
    return put(`/banners/${bannerId}`, data)
  },

  // 删除轮播图（管理员）
  deleteBanner(bannerId) {
    return del(`/banners/${bannerId}`)
  }
}

/**
 * 图片相关API（连接Java后端）
 */
const imageApi = {
  // 获取图片列表
  getImageList(params = {}) {
    return get('/images/list', params)
  },

  // 根据分类获取图片
  getImagesByCategory(category, params = {}) {
    return get(`/images/category/${category}`, params)
  },

  // 根据业务ID获取图片
  getImagesByBusinessId(businessId) {
    return get(`/images/business/${businessId}`)
  },

  // 获取图片详情
  getImageDetail(imageId) {
    return get(`/images/${imageId}`)
  },

  // 更新图片信息
  updateImage(imageId, data) {
    return put(`/images/${imageId}`, data)
  },

  // 删除图片
  deleteImage(imageId) {
    return del(`/images/${imageId}`)
  },

  // 批量删除图片
  deleteImages(imageIds) {
    return del('/images/batch', { ids: imageIds })
  }
}

/**
 * 统计相关API
 */
const statsApi = {
  // 获取首页统计数据
  getHomeStats() {
    return get('/stats/home')
  },

  // 获取销售统计
  getSalesStats(params = {}) {
    return get('/stats/sales', params)
  },

  // 获取用户统计
  getUserStats(params = {}) {
    return get('/stats/users', params)
  },

  // 获取菜品统计
  getDishStats(params = {}) {
    return get('/stats/dishes', params)
  }
}

module.exports = {
  userApi,
  dishApi,
  categoryApi,
  orderApi,
  cartApi,
  addressApi,
  bannerApi,
  imageApi,
  statsApi
}
