const jwt = require('jsonwebtoken')
const { query } = require('../config/database')

// 验证JWT token
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization']
    const token = authHeader && authHeader.split(' ')[1] // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        code: 401,
        message: '缺少访问令牌'
      })
    }

    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET)
    
    // 查询用户信息
    const users = await query(
      'SELECT id, phone, nickname, avatar, openid FROM users WHERE id = ?',
      [decoded.userId]
    )

    if (users.length === 0) {
      return res.status(401).json({
        code: 401,
        message: '用户不存在'
      })
    }

    // 将用户信息添加到请求对象
    req.user = users[0]
    next()
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        code: 401,
        message: 'Token无效'
      })
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        code: 401,
        message: 'Token已过期'
      })
    }

    console.error('Token验证错误:', error)
    return res.status(500).json({
      code: 500,
      message: '服务器错误'
    })
  }
}

// 可选的身份验证（不强制要求登录）
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization']
    const token = authHeader && authHeader.split(' ')[1]

    if (token) {
      const decoded = jwt.verify(token, process.env.JWT_SECRET)
      const users = await query(
        'SELECT id, phone, nickname, avatar, openid FROM users WHERE id = ?',
        [decoded.userId]
      )

      if (users.length > 0) {
        req.user = users[0]
      }
    }

    next()
  } catch (error) {
    // 可选认证失败时不返回错误，继续执行
    next()
  }
}

// 生成JWT token
const generateToken = (userId) => {
  return jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  )
}

// 验证管理员权限（可扩展）
const requireAdmin = async (req, res, next) => {
  try {
    // 这里可以添加管理员验证逻辑
    // 目前简单实现，可以根据需要扩展
    if (!req.user) {
      return res.status(401).json({
        code: 401,
        message: '需要登录'
      })
    }

    // 检查是否为管理员（可以通过用户表的role字段或单独的管理员表）
    // 这里暂时跳过，实际项目中需要实现
    next()
  } catch (error) {
    console.error('管理员验证错误:', error)
    return res.status(500).json({
      code: 500,
      message: '服务器错误'
    })
  }
}

module.exports = {
  authenticateToken,
  optionalAuth,
  generateToken,
  requireAdmin
}
