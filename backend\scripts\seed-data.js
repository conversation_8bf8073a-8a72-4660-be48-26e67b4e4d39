const { query } = require('../config/database')

async function seedData() {
  try {
    console.log('🌱 开始插入示例数据...')

    // 清空现有数据
    await query('DELETE FROM order_items')
    await query('DELETE FROM orders')
    await query('DELETE FROM dishes')
    await query('DELETE FROM categories')
    await query('DELETE FROM banners')
    await query('DELETE FROM users')

    // 重置自增ID
    await query('ALTER TABLE users AUTO_INCREMENT = 1')
    await query('ALTER TABLE categories AUTO_INCREMENT = 1')
    await query('ALTER TABLE dishes AUTO_INCREMENT = 1')
    await query('ALTER TABLE orders AUTO_INCREMENT = 1')
    await query('ALTER TABLE order_items AUTO_INCREMENT = 1')
    await query('ALTER TABLE banners AUTO_INCREMENT = 1')

    // 插入分类数据
    console.log('📂 插入分类数据...')
    await query(`
      INSERT INTO categories (name, icon, sort_order) VALUES
      ('热菜', '/images/categories/hot-dish.png', 100),
      ('凉菜', '/images/categories/cold-dish.png', 90),
      ('汤类', '/images/categories/soup.png', 80),
      ('主食', '/images/categories/staple.png', 70),
      ('饮品', '/images/categories/drink.png', 60),
      ('甜品', '/images/categories/dessert.png', 50)
    `)

    // 插入菜品数据
    console.log('🍽️ 插入菜品数据...')
    await query(`
      INSERT INTO dishes (category_id, name, description, price, image, is_hot, is_new, sort_order, sales_count) VALUES
      -- 热菜
      (1, '宫保鸡丁', '经典川菜，鸡肉嫩滑，花生香脆，麻辣鲜香', 28.00, '/images/dishes/gongbao-chicken.jpg', 1, 0, 100, 156),
      (1, '麻婆豆腐', '四川传统名菜，豆腐嫩滑，麻辣鲜香', 18.00, '/images/dishes/mapo-tofu.jpg', 1, 0, 95, 134),
      (1, '红烧肉', '肥而不腻，入口即化，色泽红亮', 35.00, '/images/dishes/braised-pork.jpg', 1, 0, 90, 98),
      (1, '糖醋里脊', '酸甜可口，外酥内嫩，老少皆宜', 32.00, '/images/dishes/sweet-sour-pork.jpg', 0, 1, 85, 76),
      (1, '鱼香肉丝', '川菜经典，咸甜酸辣，下饭神器', 25.00, '/images/dishes/yuxiang-pork.jpg', 1, 0, 80, 112),
      (1, '回锅肉', '四川家常菜，肥瘦相间，香辣下饭', 30.00, '/images/dishes/huiguo-pork.jpg', 0, 0, 75, 89),

      -- 凉菜
      (2, '口水鸡', '麻辣鲜香，口感嫩滑，开胃下酒', 22.00, '/images/dishes/saliva-chicken.jpg', 1, 0, 100, 89),
      (2, '凉拌黄瓜', '清爽解腻，爽脆可口，夏日必备', 8.00, '/images/dishes/cucumber-salad.jpg', 0, 0, 95, 67),
      (2, '蒜泥白肉', '肥瘦相间，蒜香浓郁，口感丰富', 26.00, '/images/dishes/garlic-pork.jpg', 0, 0, 90, 45),
      (2, '拍黄瓜', '简单清爽，开胃小菜，制作简单', 6.00, '/images/dishes/smashed-cucumber.jpg', 0, 0, 85, 78),
      (2, '凉拌木耳', '爽脆可口，营养丰富，健康美味', 12.00, '/images/dishes/wood-ear-salad.jpg', 0, 1, 80, 56),

      -- 汤类
      (3, '西红柿鸡蛋汤', '酸甜开胃，营养丰富，家常美味', 12.00, '/images/dishes/tomato-egg-soup.jpg', 0, 0, 100, 134),
      (3, '冬瓜排骨汤', '清淡鲜美，滋补养生，老火靓汤', 28.00, '/images/dishes/wintermelon-soup.jpg', 1, 0, 95, 67),
      (3, '紫菜蛋花汤', '清淡鲜美，制作简单，营养均衡', 10.00, '/images/dishes/seaweed-soup.jpg', 0, 0, 90, 89),
      (3, '酸辣汤', '酸辣开胃，口感丰富，暖胃佳品', 15.00, '/images/dishes/hot-sour-soup.jpg', 0, 1, 85, 72),

      -- 主食
      (4, '扬州炒饭', '粒粒分明，香味浓郁，经典炒饭', 15.00, '/images/dishes/yangzhou-rice.jpg', 1, 0, 100, 156),
      (4, '牛肉面', '汤浓面劲，牛肉鲜美，分量十足', 18.00, '/images/dishes/beef-noodles.jpg', 1, 0, 95, 123),
      (4, '小笼包', '皮薄馅大，汤汁鲜美，江南名点', 16.00, '/images/dishes/xiaolongbao.jpg', 0, 1, 90, 98),
      (4, '白米饭', '优质大米，香甜可口，粒粒饱满', 3.00, '/images/dishes/white-rice.jpg', 0, 0, 85, 234),
      (4, '蛋炒饭', '简单美味，蛋香浓郁，经济实惠', 12.00, '/images/dishes/egg-rice.jpg', 0, 0, 80, 145),

      -- 饮品
      (5, '鲜榨橙汁', '新鲜橙子现榨，维C丰富，酸甜可口', 12.00, '/images/dishes/orange-juice.jpg', 0, 1, 100, 67),
      (5, '柠檬蜂蜜茶', '酸甜清香，美容养颜，生津止渴', 15.00, '/images/dishes/lemon-tea.jpg', 1, 0, 95, 89),
      (5, '可乐', '经典碳酸饮料，清爽解腻', 6.00, '/images/dishes/cola.jpg', 0, 0, 90, 145),
      (5, '绿茶', '清香淡雅，解腻消食，天然健康', 8.00, '/images/dishes/green-tea.jpg', 0, 0, 85, 78),
      (5, '酸梅汤', '酸甜解腻，生津止渴，传统饮品', 10.00, '/images/dishes/plum-juice.jpg', 0, 1, 80, 54),

      -- 甜品
      (6, '红豆沙', '香甜软糯，传统甜品，温润滋补', 8.00, '/images/dishes/red-bean-soup.jpg', 0, 0, 100, 56),
      (6, '芒果布丁', '香甜顺滑，果香浓郁，颜值很高', 12.00, '/images/dishes/mango-pudding.jpg', 0, 1, 95, 43),
      (6, '提拉米苏', '意式经典，层次丰富，浓郁香甜', 25.00, '/images/dishes/tiramisu.jpg', 1, 1, 90, 34),
      (6, '双皮奶', '奶香浓郁，口感顺滑，广式甜品', 10.00, '/images/dishes/double-skin-milk.jpg', 0, 0, 85, 67),
      (6, '绿豆沙', '清热解毒，甘甜爽口，夏日首选', 8.00, '/images/dishes/mung-bean-soup.jpg', 0, 0, 80, 45)
    `)

    // 插入轮播图数据
    console.log('🖼️ 插入轮播图数据...')
    await query(`
      INSERT INTO banners (title, image, link_type, link_id, sort_order) VALUES
      ('新品上市', '/images/banners/banner1.jpg', 'category', 1, 100),
      ('特价优惠', '/images/banners/banner2.jpg', 'dish', 1, 90),
      ('招牌菜品', '/images/banners/banner3.jpg', 'category', 2, 80),
      ('夏日饮品', '/images/banners/banner4.jpg', 'category', 5, 70)
    `)

    // 插入测试用户
    console.log('👤 插入测试用户...')
    await query(`
      INSERT INTO users (phone, nickname, avatar) VALUES
      ('13800138000', '测试用户', '/images/avatars/default.png'),
      ('13800138001', '美食达人', '/images/avatars/user1.png'),
      ('13800138002', '吃货小王', '/images/avatars/user2.png')
    `)

    console.log('✅ 示例数据插入完成！')
    console.log('')
    console.log('📊 数据统计:')
    
    const stats = await Promise.all([
      query('SELECT COUNT(*) as count FROM categories'),
      query('SELECT COUNT(*) as count FROM dishes'),
      query('SELECT COUNT(*) as count FROM banners'),
      query('SELECT COUNT(*) as count FROM users')
    ])
    
    console.log(`   分类: ${stats[0][0].count} 个`)
    console.log(`   菜品: ${stats[1][0].count} 个`)
    console.log(`   轮播图: ${stats[2][0].count} 个`)
    console.log(`   用户: ${stats[3][0].count} 个`)
    console.log('')
    console.log('🎉 数据库初始化完成！可以开始使用系统了。')

  } catch (error) {
    console.error('❌ 插入示例数据失败:', error)
    throw error
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  seedData()
    .then(() => process.exit(0))
    .catch(() => process.exit(1))
}

module.exports = { seedData }
