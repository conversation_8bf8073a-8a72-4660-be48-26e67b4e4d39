// pages/menu/menu.js
const app = getApp()
const { categoryApi, dishApi } = require('../../utils/api')

Page({
  data: {
    // 页面状态
    loading: true,
    refreshing: false,
    loadingMore: false,
    hasMore: true,
    
    // 搜索相关
    searchKeyword: '',
    
    // 分类数据
    categories: [],
    selectedCategory: null,
    currentCategoryName: '',
    
    // 菜品数据
    dishes: [],
    page: 1,
    pageSize: 10,
    
    // 购物车数据
    cartCount: 0,
    cartTotal: 0,
    
    // 配置
    deliveryFee: 5,
    minOrderAmount: 20
  },

  onLoad(options) {
    console.log('菜单页面加载', options)
    
    // 获取传递的分类ID
    if (options.categoryId) {
      this.setData({ selectedCategory: parseInt(options.categoryId) })
    }
    
    this.initPage()
  },

  onShow() {
    console.log('菜单页面显示')
    this.updateCartInfo()
  },

  onPullDownRefresh() {
    console.log('下拉刷新')
    this.onRefresh()
  },

  onReachBottom() {
    console.log('触底加载')
    this.onLoadMore()
  },

  // 初始化页面
  async initPage() {
    this.setData({ loading: true })
    
    try {
      await this.loadCategories()
      await this.loadDishes(true)
    } catch (error) {
      console.error('页面初始化失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载分类
  async loadCategories() {
    try {
      // 模拟分类数据
      const categories = [
        { id: 0, name: '全部', dishCount: 0 },
        { id: 1, name: '川菜', dishCount: 8 },
        { id: 2, name: '粤菜', dishCount: 6 },
        { id: 3, name: '湘菜', dishCount: 5 },
        { id: 4, name: '鲁菜', dishCount: 4 },
        { id: 5, name: '苏菜', dishCount: 3 },
        { id: 6, name: '浙菜', dishCount: 4 }
      ]
      
      // 如果没有选择分类，默认选择第一个
      if (this.data.selectedCategory === null) {
        this.setData({ selectedCategory: categories[0].id })
      }
      
      this.setData({ categories })
      this.updateCurrentCategoryName()
    } catch (error) {
      console.error('加载分类失败:', error)
    }
  },

  // 加载菜品
  async loadDishes(reset = false) {
    if (reset) {
      this.setData({ 
        page: 1, 
        dishes: [], 
        hasMore: true 
      })
    }

    if (!this.data.hasMore && !reset) {
      return
    }

    this.setData({ loadingMore: true })

    try {
      // 模拟菜品数据
      const mockDishes = this.getMockDishes()
      
      // 根据分类过滤
      let filteredDishes = mockDishes
      if (this.data.selectedCategory > 0) {
        filteredDishes = mockDishes.filter(dish => dish.categoryId === this.data.selectedCategory)
      }
      
      // 分页处理
      const startIndex = (this.data.page - 1) * this.data.pageSize
      const endIndex = startIndex + this.data.pageSize
      const pageDishes = filteredDishes.slice(startIndex, endIndex)
      
      // 添加购物车数量信息
      const dishesWithCart = pageDishes.map(dish => ({
        ...dish,
        cartQuantity: this.getCartQuantity(dish.id)
      }))
      
      const newDishes = reset ? dishesWithCart : [...this.data.dishes, ...dishesWithCart]
      const hasMore = endIndex < filteredDishes.length
      
      this.setData({
        dishes: newDishes,
        hasMore: hasMore,
        page: this.data.page + 1
      })
      
    } catch (error) {
      console.error('加载菜品失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loadingMore: false })
    }
  },

  // 获取模拟菜品数据
  getMockDishes() {
    return [
      {
        id: 1,
        name: '麻婆豆腐',
        description: '经典川菜，麻辣鲜香，嫩滑爽口，配料丰富',
        price: 28.00,
        originalPrice: 35.00,
        image: 'https://via.placeholder.com/200x200/ff6b35/ffffff?text=麻婆豆腐',
        categoryId: 1,
        salesCount: 156,
        rating: 4.8,
        isHot: true,
        isNew: false,
        isRecommended: true
      },
      {
        id: 2,
        name: '宫保鸡丁',
        description: '传统川菜，酸甜微辣，鸡肉嫩滑，花生香脆',
        price: 32.00,
        image: 'https://via.placeholder.com/200x200/f7931e/ffffff?text=宫保鸡丁',
        categoryId: 1,
        salesCount: 89,
        rating: 4.7,
        isHot: false,
        isNew: true,
        isRecommended: false
      },
      {
        id: 3,
        name: '白切鸡',
        description: '经典粤菜，清淡鲜美，肉质鲜嫩，蘸料丰富',
        price: 45.00,
        image: 'https://via.placeholder.com/200x200/2ecc71/ffffff?text=白切鸡',
        categoryId: 2,
        salesCount: 67,
        rating: 4.6,
        isHot: false,
        isNew: false,
        isRecommended: true
      },
      {
        id: 4,
        name: '剁椒鱼头',
        description: '湘菜名菜，香辣开胃，鱼肉鲜美，汤汁浓郁',
        price: 68.00,
        originalPrice: 78.00,
        image: 'https://via.placeholder.com/200x200/74b9ff/ffffff?text=剁椒鱼头',
        categoryId: 3,
        salesCount: 234,
        rating: 4.9,
        isHot: true,
        isNew: false,
        isRecommended: true
      },
      {
        id: 5,
        name: '糖醋里脊',
        description: '酸甜可口，外酥内嫩，色泽金黄，老少皆宜',
        price: 36.00,
        image: 'https://via.placeholder.com/200x200/a29bfe/ffffff?text=糖醋里脊',
        categoryId: 1,
        salesCount: 123,
        rating: 4.5,
        isHot: false,
        isNew: true,
        isRecommended: false
      },
      {
        id: 6,
        name: '红烧肉',
        description: '传统名菜，肥而不腻，入口即化，色泽红亮',
        price: 42.00,
        image: 'https://via.placeholder.com/200x200/fd79a8/ffffff?text=红烧肉',
        categoryId: 4,
        salesCount: 178,
        rating: 4.8,
        isHot: true,
        isNew: false,
        isRecommended: false
      }
    ]
  },

  // 获取购物车中商品数量
  getCartQuantity(dishId) {
    const cartItem = app.globalData.cart.find(item => item.id === dishId)
    return cartItem ? cartItem.quantity : 0
  },

  // 更新购物车信息
  updateCartInfo() {
    const cartCount = app.getCartItemCount()
    const cartTotal = app.globalData.cartTotal
    
    this.setData({ 
      cartCount, 
      cartTotal: cartTotal.toFixed(2)
    })
    
    // 更新菜品的购物车数量
    const updatedDishes = this.data.dishes.map(dish => ({
      ...dish,
      cartQuantity: this.getCartQuantity(dish.id)
    }))
    
    this.setData({ dishes: updatedDishes })
  },

  // 更新当前分类名称
  updateCurrentCategoryName() {
    const category = this.data.categories.find(cat => cat.id === this.data.selectedCategory)
    this.setData({ 
      currentCategoryName: category ? category.name : ''
    })
  },

  // 搜索点击
  onSearchTap() {
    wx.navigateTo({
      url: '/pages/search/search'
    })
  },

  // 分类选择
  onCategorySelect(e) {
    const category = e.currentTarget.dataset.category
    console.log('选择分类:', category)
    
    this.setData({ selectedCategory: category.id })
    this.updateCurrentCategoryName()
    this.loadDishes(true)
  },

  // 菜品点击
  onDishTap(e) {
    const dish = e.currentTarget.dataset.dish
    console.log('菜品点击:', dish)
    
    wx.navigateTo({
      url: `/pages/dish-detail/dish-detail?id=${dish.id}`
    })
  },

  // 添加到购物车
  onAddToCart(e) {
    e.stopPropagation()
    
    const dish = e.currentTarget.dataset.dish
    console.log('添加到购物车:', dish)
    
    app.addToCart(dish, 1)
    this.updateCartInfo()
  },

  // 增加数量
  onIncreaseQuantity(e) {
    e.stopPropagation()
    
    const dish = e.currentTarget.dataset.dish
    console.log('增加数量:', dish)
    
    app.updateCartQuantity(dish.id, dish.cartQuantity + 1)
    this.updateCartInfo()
  },

  // 减少数量
  onDecreaseQuantity(e) {
    e.stopPropagation()
    
    const dish = e.currentTarget.dataset.dish
    console.log('减少数量:', dish)
    
    if (dish.cartQuantity > 1) {
      app.updateCartQuantity(dish.id, dish.cartQuantity - 1)
    } else {
      app.removeFromCart(dish.id)
    }
    
    this.updateCartInfo()
  },

  // 刷新
  onRefresh() {
    this.setData({ refreshing: true })
    
    this.loadDishes(true).then(() => {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    })
  },

  // 加载更多
  onLoadMore() {
    if (!this.data.loadingMore && this.data.hasMore) {
      this.loadDishes(false)
    }
  },

  // 购物车点击
  onCartTap() {
    wx.switchTab({
      url: '/pages/cart/cart'
    })
  },

  // 去结算
  onCheckout() {
    if (this.data.cartTotal >= this.data.minOrderAmount) {
      wx.navigateTo({
        url: '/pages/order/order'
      })
    } else {
      wx.showToast({
        title: `还差¥${(this.data.minOrderAmount - this.data.cartTotal).toFixed(2)}起送`,
        icon: 'none'
      })
    }
  }
})
