<!--pages/cart/cart.wxml-->
<view class="container">
  <!-- 购物车列表 -->
  <view class="cart-content" wx:if="{{cartItems.length > 0}}">
    <!-- 商家信息 -->
    <view class="merchant-info">
      <view class="merchant-header">
        <text class="merchant-name">美食餐厅</text>
        <text class="merchant-status">营业中</text>
      </view>
      <text class="merchant-desc">预计30分钟送达</text>
    </view>

    <!-- 购物车商品列表 -->
    <view class="cart-list">
      <view 
        class="cart-item" 
        wx:for="{{cartItems}}" 
        wx:key="id"
      >
        <image class="item-image" src="{{item.image}}" mode="aspectFill" />
        <view class="item-info">
          <text class="item-name">{{item.name}}</text>
          <text class="item-desc">{{item.description}}</text>
          <view class="item-footer">
            <text class="item-price">
              <text class="price-symbol">¥</text>{{item.price}}
            </text>
            
            <!-- 数量控制器 -->
            <view class="quantity-control">
              <view 
                class="quantity-btn" 
                bindtap="onDecreaseQuantity"
                data-item="{{item}}"
              >
                <icon type="minus" size="14" color="#ff6b35"></icon>
              </view>
              <text class="quantity-text">{{item.quantity}}</text>
              <view 
                class="quantity-btn" 
                bindtap="onIncreaseQuantity"
                data-item="{{item}}"
              >
                <icon type="plus" size="14" color="#ff6b35"></icon>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 优惠券 -->
    <view class="coupon-section">
      <view class="coupon-item" bindtap="onSelectCoupon">
        <view class="coupon-left">
          <icon type="success" size="20" color="#ff6b35"></icon>
          <text class="coupon-text">优惠券</text>
        </view>
        <view class="coupon-right">
          <text class="coupon-desc" wx:if="{{selectedCoupon}}">
            已选择：{{selectedCoupon.name}}
          </text>
          <text class="coupon-desc" wx:else>
            {{availableCoupons.length}}张可用
          </text>
          <icon type="arrow" size="16" color="#999"></icon>
        </view>
      </view>
    </view>

    <!-- 配送信息 */
    <view class="delivery-section">
      <view class="delivery-item" bindtap="onSelectAddress">
        <view class="delivery-left">
          <icon type="location" size="20" color="#ff6b35"></icon>
          <view class="delivery-info">
            <text class="delivery-title">配送地址</text>
            <text class="delivery-desc" wx:if="{{selectedAddress}}">
              {{selectedAddress.detail}}
            </text>
            <text class="delivery-desc" wx:else>
              请选择配送地址
            </text>
          </view>
        </view>
        <icon type="arrow" size="16" color="#999"></icon>
      </view>
      
      <view class="delivery-item">
        <view class="delivery-left">
          <icon type="clock" size="20" color="#ff6b35"></icon>
          <view class="delivery-info">
            <text class="delivery-title">配送时间</text>
            <text class="delivery-desc">预计{{deliveryTime}}分钟送达</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 备注 -->
    <view class="remark-section">
      <view class="remark-header">
        <text class="remark-title">备注</text>
      </view>
      <textarea 
        class="remark-input" 
        placeholder="请输入备注信息（选填）"
        value="{{remark}}"
        bindinput="onRemarkInput"
        maxlength="100"
      />
    </view>

    <!-- 费用明细 -->
    <view class="cost-section">
      <view class="cost-item">
        <text class="cost-label">商品小计</text>
        <text class="cost-value">¥{{subtotal}}</text>
      </view>
      <view class="cost-item">
        <text class="cost-label">配送费</text>
        <text class="cost-value">¥{{deliveryFee}}</text>
      </view>
      <view class="cost-item" wx:if="{{couponDiscount > 0}}">
        <text class="cost-label">优惠券</text>
        <text class="cost-value discount">-¥{{couponDiscount}}</text>
      </view>
      <view class="cost-divider"></view>
      <view class="cost-item total">
        <text class="cost-label">实付金额</text>
        <text class="cost-value">¥{{totalAmount}}</text>
      </view>
    </view>
  </view>

  <!-- 空购物车 -->
  <view class="empty-cart" wx:else>
    <image class="empty-icon" src="/images/empty-cart.png" />
    <text class="empty-text">购物车是空的</text>
    <text class="empty-desc">快去选购美食吧</text>
    <button class="go-shopping-btn" bindtap="onGoShopping">
      去逛逛
    </button>
  </view>

  <!-- 底部结算栏 -->
  <view class="checkout-bar" wx:if="{{cartItems.length > 0}}">
    <view class="checkout-info">
      <text class="total-price">
        合计：<text class="price-highlight">¥{{totalAmount}}</text>
      </text>
      <text class="item-count">共{{totalQuantity}}件商品</text>
    </view>
    <button 
      class="checkout-btn {{totalAmount >= minOrderAmount ? 'active' : 'disabled'}}"
      bindtap="onCheckout"
    >
      {{totalAmount >= minOrderAmount ? '去结算' : '还差¥' + (minOrderAmount - totalAmount).toFixed(2)}}
    </button>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area"></view>
</view>

<!-- 清空购物车确认弹窗 -->
<modal 
  title="确认清空购物车？" 
  confirm-text="确认" 
  cancel-text="取消"
  hidden="{{!showClearModal}}"
  bindconfirm="onConfirmClear"
  bindcancel="onCancelClear"
>
  清空后将无法恢复
</modal>
