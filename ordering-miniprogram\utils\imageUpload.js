// utils/imageUpload.js - 图片上传工具（连接Java后端）

const { upload } = require('./request')

/**
 * 图片上传配置
 */
const config = {
  // 图片分类
  categories: {
    DISH: 'dish',        // 菜品图片
    BANNER: 'banner',    // 轮播图
    AVATAR: 'avatar',    // 头像
    OTHER: 'other'       // 其他
  },
  
  // 上传限制
  maxSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  maxCount: 9 // 最大选择数量
}

/**
 * 选择并上传单张图片
 * @param {Object} options 配置选项
 * @param {string} options.category 图片分类
 * @param {number} options.businessId 业务ID
 * @param {string} options.description 图片描述
 * @param {number} options.count 选择数量，默认1
 * @returns {Promise} 上传结果
 */
function chooseAndUploadImage(options = {}) {
  return new Promise((resolve, reject) => {
    const {
      category = config.categories.OTHER,
      businessId,
      description,
      count = 1
    } = options

    // 选择图片
    wx.chooseImage({
      count: Math.min(count, config.maxCount),
      sizeType: ['compressed', 'original'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        console.log('选择图片成功:', res.tempFilePaths)
        
        if (count === 1) {
          // 单张上传
          uploadSingleImage(res.tempFilePaths[0], {
            category,
            businessId,
            description
          }).then(resolve).catch(reject)
        } else {
          // 批量上传
          uploadMultipleImages(res.tempFilePaths, {
            category,
            businessId,
            description
          }).then(resolve).catch(reject)
        }
      },
      fail: (error) => {
        console.error('选择图片失败:', error)
        reject(new Error('选择图片失败'))
      }
    })
  })
}

/**
 * 上传单张图片到Java后端
 * @param {string} filePath 图片路径
 * @param {Object} options 配置选项
 * @returns {Promise} 上传结果
 */
function uploadSingleImage(filePath, options = {}) {
  return new Promise((resolve, reject) => {
    const {
      category = config.categories.OTHER,
      businessId,
      description
    } = options

    // 构建表单数据
    const formData = {
      category
    }
    
    if (businessId) {
      formData.businessId = businessId.toString()
    }
    
    if (description) {
      formData.description = description
    }

    // 上传到Java后端
    upload('/images/upload', filePath, formData, {
      name: 'file',
      loadingText: '上传中...',
      onProgress: (res) => {
        console.log('上传进度:', res.progress + '%')
      }
    }).then((data) => {
      console.log('图片上传成功:', data.data)
      wx.showToast({
        title: '上传成功',
        icon: 'success'
      })
      resolve(data.data)
    }).catch((error) => {
      console.error('图片上传失败:', error)
      reject(error)
    })
  })
}

/**
 * 批量上传图片
 * @param {Array} filePaths 图片路径数组
 * @param {Object} options 配置选项
 * @returns {Promise} 上传结果
 */
function uploadMultipleImages(filePaths, options = {}) {
  return new Promise((resolve, reject) => {
    const results = []
    let completed = 0
    let hasError = false

    wx.showLoading({
      title: `上传中 0/${filePaths.length}`,
      mask: true
    })

    // 逐个上传
    filePaths.forEach((filePath, index) => {
      uploadSingleImageInternal(filePath, options)
        .then((result) => {
          if (!hasError) {
            results[index] = result
            completed++
            
            wx.showLoading({
              title: `上传中 ${completed}/${filePaths.length}`,
              mask: true
            })
            
            if (completed === filePaths.length) {
              wx.hideLoading()
              wx.showToast({
                title: `成功上传${completed}张图片`,
                icon: 'success'
              })
              resolve(results.filter(item => item)) // 过滤空值
            }
          }
        })
        .catch((error) => {
          if (!hasError) {
            hasError = true
            wx.hideLoading()
            console.error(`第${index + 1}张图片上传失败:`, error)
            wx.showToast({
              title: `第${index + 1}张图片上传失败`,
              icon: 'none'
            })
            reject(error)
          }
        })
    })
  })
}

/**
 * 内部上传单张图片（不显示提示）
 */
function uploadSingleImageInternal(filePath, options = {}) {
  const {
    category = config.categories.OTHER,
    businessId,
    description
  } = options

  const formData = {
    category
  }
  
  if (businessId) {
    formData.businessId = businessId.toString()
  }
  
  if (description) {
    formData.description = description
  }

  return upload('/images/upload', filePath, formData, {
    name: 'file',
    loading: false,
    showError: false
  }).then(data => data.data)
}

/**
 * 预览图片
 * @param {string} current 当前图片URL
 * @param {Array} urls 图片URL数组
 */
function previewImage(current, urls = []) {
  wx.previewImage({
    current: current,
    urls: urls.length > 0 ? urls : [current]
  })
}

/**
 * 获取图片信息
 * @param {string} src 图片路径
 * @returns {Promise} 图片信息
 */
function getImageInfo(src) {
  return new Promise((resolve, reject) => {
    wx.getImageInfo({
      src: src,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 压缩图片
 * @param {string} src 图片路径
 * @param {Object} options 压缩选项
 * @returns {Promise} 压缩后的图片路径
 */
function compressImage(src, options = {}) {
  return new Promise((resolve, reject) => {
    const {
      quality = 80,
      width,
      height
    } = options

    wx.compressImage({
      src: src,
      quality: quality,
      width: width,
      height: height,
      success: (res) => {
        resolve(res.tempFilePath)
      },
      fail: reject
    })
  })
}

/**
 * 保存图片到相册
 * @param {string} filePath 图片路径
 * @returns {Promise} 保存结果
 */
function saveImageToPhotosAlbum(filePath) {
  return new Promise((resolve, reject) => {
    wx.saveImageToPhotosAlbum({
      filePath: filePath,
      success: () => {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        })
        resolve()
      },
      fail: (error) => {
        if (error.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '提示',
            content: '需要授权保存图片到相册',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting()
              }
            }
          })
        } else {
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          })
        }
        reject(error)
      }
    })
  })
}

/**
 * 从Java后端获取图片列表
 * @param {Object} options 查询选项
 * @returns {Promise} 图片列表
 */
function getImageList(options = {}) {
  const { get } = require('./request')
  
  const {
    category,
    businessId,
    page = 1,
    size = 10
  } = options

  let url = '/images/list'
  
  if (category && businessId) {
    url = `/images/category/${category}/business/${businessId}`
  } else if (category) {
    url = `/images/category/${category}`
  } else if (businessId) {
    url = `/images/business/${businessId}`
  }

  return get(url, { page, size })
}

/**
 * 删除图片
 * @param {number} imageId 图片ID
 * @returns {Promise} 删除结果
 */
function deleteImage(imageId) {
  const { delete: del } = require('./request')
  return del(`/images/${imageId}`)
}

/**
 * 批量删除图片
 * @param {Array} imageIds 图片ID数组
 * @returns {Promise} 删除结果
 */
function deleteImages(imageIds) {
  const { delete: del } = require('./request')
  return del('/images/batch', { ids: imageIds })
}

module.exports = {
  config,
  chooseAndUploadImage,
  uploadSingleImage,
  uploadMultipleImages,
  previewImage,
  getImageInfo,
  compressImage,
  saveImageToPhotosAlbum,
  getImageList,
  deleteImage,
  deleteImages
}
