// components/floating-cart/floating-cart.js
Component({
  properties: {
    cartCount: {
      type: Number,
      value: 0
    },
    totalPrice: {
      type: Number,
      value: 0
    },
    show: {
      type: <PERSON><PERSON>an,
      value: false
    }
  },

  data: {
    animationData: {},
    bounceAnimation: {}
  },

  observers: {
    'cartCount': function(newVal, oldVal) {
      if (newVal > oldVal && newVal > 0) {
        this.playBounceAnimation()
      }
    }
  },

  methods: {
    // 播放弹跳动画
    playBounceAnimation() {
      const animation = wx.createAnimation({
        duration: 600,
        timingFunction: 'ease-out'
      })

      // 弹跳效果
      animation.scale(1.2).step({ duration: 200 })
      animation.scale(1).step({ duration: 200 })
      animation.scale(1.1).step({ duration: 100 })
      animation.scale(1).step({ duration: 100 })

      this.setData({
        bounceAnimation: animation.export()
      })

      // 重置动画
      setTimeout(() => {
        this.setData({
          bounceAnimation: {}
        })
      }, 600)
    },

    // 显示/隐藏动画
    toggleShow() {
      const animation = wx.createAnimation({
        duration: 300,
        timingFunction: 'ease-out'
      })

      if (this.data.show) {
        animation.translateY(0).opacity(1).step()
      } else {
        animation.translateY(100).opacity(0).step()
      }

      this.setData({
        animationData: animation.export()
      })
    },

    // 点击购物车
    onCartTap() {
      this.triggerEvent('cartTap')
    },

    // 点击结算
    onCheckoutTap() {
      this.triggerEvent('checkoutTap')
    }
  },

  lifetimes: {
    attached() {
      this.toggleShow()
    }
  }
})
