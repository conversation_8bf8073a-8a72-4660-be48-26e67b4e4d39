// pages/cart/cart.js
const { showToast, showConfirm } = require('../../utils/util')

Page({
  data: {
    cartItems: [],
    totalPrice: 0,
    totalCount: 0,
    selectedItems: [],
    isAllSelected: false,
    isEditMode: false,
    isEmpty: true
  },

  onLoad() {
    this.loadCartData()
  },

  onShow() {
    this.loadCartData()
  },

  // 加载购物车数据
  loadCartData() {
    const app = getApp()
    const cartData = app.getCartData()
    
    this.setData({
      cartItems: cartData.cart,
      totalPrice: cartData.totalPrice,
      totalCount: cartData.totalCount,
      isEmpty: cartData.cart.length === 0,
      selectedItems: cartData.cart.map(item => item.id), // 默认全选
      isAllSelected: cartData.cart.length > 0
    })
    
    this.calculateSelectedTotal()
  },

  // 计算选中商品总价
  calculateSelectedTotal() {
    const { cartItems, selectedItems } = this.data
    
    let totalPrice = 0
    let totalCount = 0
    
    cartItems.forEach(item => {
      if (selectedItems.includes(item.id)) {
        totalPrice += item.price * item.quantity
        totalCount += item.quantity
      }
    })
    
    this.setData({
      totalPrice: parseFloat(totalPrice.toFixed(2)),
      totalCount
    })
  },

  // 商品选择切换
  onItemSelect(e) {
    const { id } = e.currentTarget.dataset
    let { selectedItems } = this.data
    
    if (selectedItems.includes(id)) {
      selectedItems = selectedItems.filter(itemId => itemId !== id)
    } else {
      selectedItems.push(id)
    }
    
    this.setData({
      selectedItems,
      isAllSelected: selectedItems.length === this.data.cartItems.length
    })
    
    this.calculateSelectedTotal()
  },

  // 全选切换
  onSelectAll() {
    const isAllSelected = !this.data.isAllSelected
    const selectedItems = isAllSelected ? this.data.cartItems.map(item => item.id) : []
    
    this.setData({
      isAllSelected,
      selectedItems
    })
    
    this.calculateSelectedTotal()
  },

  // 数量减少
  onQuantityDecrease(e) {
    const { id } = e.currentTarget.dataset
    const app = getApp()
    const item = this.data.cartItems.find(item => item.id === id)
    
    if (item && item.quantity > 1) {
      app.updateCartItem(id, item.quantity - 1)
      this.loadCartData()
    } else {
      this.onRemoveItem(e)
    }
  },

  // 数量增加
  onQuantityIncrease(e) {
    const { id } = e.currentTarget.dataset
    const app = getApp()
    const item = this.data.cartItems.find(item => item.id === id)
    
    if (item) {
      app.updateCartItem(id, item.quantity + 1)
      this.loadCartData()
    }
  },

  // 数量输入
  onQuantityInput(e) {
    const { id } = e.currentTarget.dataset
    let quantity = parseInt(e.detail.value) || 1
    
    if (quantity < 1) quantity = 1
    if (quantity > 99) quantity = 99
    
    const app = getApp()
    app.updateCartItem(id, quantity)
    this.loadCartData()
  },

  // 删除商品
  async onRemoveItem(e) {
    const { id } = e.currentTarget.dataset
    
    try {
      await showConfirm('确定要删除这个商品吗？')
      
      const app = getApp()
      app.removeFromCart(id)
      
      // 更新选中状态
      let { selectedItems } = this.data
      selectedItems = selectedItems.filter(itemId => itemId !== id)
      
      this.setData({ selectedItems })
      this.loadCartData()
      
      showToast('商品已删除', 'success')
      
    } catch (error) {
      // 用户取消删除
    }
  },

  // 批量删除选中商品
  async onRemoveSelected() {
    const { selectedItems } = this.data
    
    if (selectedItems.length === 0) {
      showToast('请选择要删除的商品', 'none')
      return
    }
    
    try {
      await showConfirm(`确定要删除选中的 ${selectedItems.length} 个商品吗？`)
      
      const app = getApp()
      app.removeMultipleFromCart(selectedItems)
      
      this.setData({
        selectedItems: [],
        isAllSelected: false,
        isEditMode: false
      })
      
      this.loadCartData()
      showToast('商品已删除', 'success')
      
    } catch (error) {
      // 用户取消删除
    }
  },

  // 清空购物车
  async onClearCart() {
    if (this.data.isEmpty) return
    
    try {
      await showConfirm('确定要清空购物车吗？')
      
      const app = getApp()
      app.clearCart()
      
      this.setData({
        selectedItems: [],
        isAllSelected: false,
        isEditMode: false
      })
      
      this.loadCartData()
      showToast('购物车已清空', 'success')
      
    } catch (error) {
      // 用户取消清空
    }
  },

  // 切换编辑模式
  onToggleEditMode() {
    this.setData({
      isEditMode: !this.data.isEditMode
    })
  },

  // 商品点击
  onItemTap(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/detail/detail?id=${id}`
    })
  },

  // 去结算
  onCheckout() {
    const { selectedItems, totalCount, totalPrice } = this.data
    
    if (selectedItems.length === 0) {
      showToast('请选择要结算的商品', 'none')
      return
    }
    
    if (totalPrice <= 0) {
      showToast('结算金额不能为0', 'none')
      return
    }
    
    // 跳转到订单确认页面
    wx.navigateTo({
      url: `/pages/order/order?selectedItems=${JSON.stringify(selectedItems)}`
    })
  },

  // 继续购物
  onContinueShopping() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  // 推荐商品点击
  onRecommendTap() {
    wx.switchTab({
      url: '/pages/category/category'
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadCartData()
    wx.stopPullDownRefresh()
  }
})
