// pages/admin/dish-manage/dish-manage.js
const app = getApp()
const { dishApi, categoryApi } = require('../../../utils/api')
const { chooseAndUploadImage, previewImage } = require('../../../utils/imageUpload')

Page({
  data: {
    // 页面状态
    loading: true,
    loadingMore: false,
    hasMore: true,
    
    // 搜索和筛选
    searchKeyword: '',
    selectedCategory: 0,
    
    // 数据
    categories: [],
    dishes: [],
    page: 1,
    pageSize: 10
  },

  onLoad() {
    console.log('菜品管理页面加载')
    this.initPage()
  },

  onShow() {
    console.log('菜品管理页面显示')
    // 刷新数据（可能从编辑页面返回）
    this.loadDishes(true)
  },

  onPullDownRefresh() {
    console.log('下拉刷新')
    this.loadDishes(true).then(() => {
      wx.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    console.log('触底加载')
    this.loadDishes(false)
  },

  // 初始化页面
  async initPage() {
    this.setData({ loading: true })
    
    try {
      await this.loadCategories()
      await this.loadDishes(true)
    } catch (error) {
      console.error('页面初始化失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载分类
  async loadCategories() {
    try {
      // 模拟分类数据
      const categories = [
        { id: 1, name: '川菜' },
        { id: 2, name: '粤菜' },
        { id: 3, name: '湘菜' },
        { id: 4, name: '鲁菜' },
        { id: 5, name: '苏菜' },
        { id: 6, name: '浙菜' }
      ]
      
      this.setData({ categories })
    } catch (error) {
      console.error('加载分类失败:', error)
    }
  },

  // 加载菜品
  async loadDishes(reset = false) {
    if (reset) {
      this.setData({ 
        page: 1, 
        dishes: [], 
        hasMore: true 
      })
    }

    if (!this.data.hasMore && !reset) {
      return
    }

    this.setData({ loadingMore: true })

    try {
      // 模拟菜品数据
      const mockDishes = this.getMockDishes()
      
      // 根据搜索关键词过滤
      let filteredDishes = mockDishes
      if (this.data.searchKeyword) {
        filteredDishes = mockDishes.filter(dish => 
          dish.name.includes(this.data.searchKeyword)
        )
      }
      
      // 根据分类过滤
      if (this.data.selectedCategory > 0) {
        filteredDishes = filteredDishes.filter(dish => 
          dish.categoryId === this.data.selectedCategory
        )
      }
      
      // 分页处理
      const startIndex = (this.data.page - 1) * this.data.pageSize
      const endIndex = startIndex + this.data.pageSize
      const pageDishes = filteredDishes.slice(startIndex, endIndex)
      
      const newDishes = reset ? pageDishes : [...this.data.dishes, ...pageDishes]
      const hasMore = endIndex < filteredDishes.length
      
      this.setData({
        dishes: newDishes,
        hasMore: hasMore,
        page: this.data.page + 1
      })
      
    } catch (error) {
      console.error('加载菜品失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loadingMore: false })
    }
  },

  // 获取模拟菜品数据
  getMockDishes() {
    return [
      {
        id: 1,
        name: '麻婆豆腐',
        description: '经典川菜，麻辣鲜香，嫩滑爽口',
        price: 28.00,
        originalPrice: 35.00,
        image: 'https://via.placeholder.com/200x200/ff6b35/ffffff?text=麻婆豆腐',
        categoryId: 1,
        categoryName: '川菜',
        salesCount: 156,
        rating: 4.8,
        status: 1,
        isHot: true,
        isNew: false,
        isRecommended: true
      },
      {
        id: 2,
        name: '宫保鸡丁',
        description: '传统川菜，酸甜微辣，鸡肉嫩滑',
        price: 32.00,
        image: 'https://via.placeholder.com/200x200/f7931e/ffffff?text=宫保鸡丁',
        categoryId: 1,
        categoryName: '川菜',
        salesCount: 89,
        rating: 4.7,
        status: 1,
        isHot: false,
        isNew: true,
        isRecommended: false
      },
      {
        id: 3,
        name: '白切鸡',
        description: '经典粤菜，清淡鲜美，肉质鲜嫩',
        price: 45.00,
        image: 'https://via.placeholder.com/200x200/2ecc71/ffffff?text=白切鸡',
        categoryId: 2,
        categoryName: '粤菜',
        salesCount: 67,
        rating: 4.6,
        status: 0,
        isHot: false,
        isNew: false,
        isRecommended: true
      },
      {
        id: 4,
        name: '剁椒鱼头',
        description: '湘菜名菜，香辣开胃，鱼肉鲜美',
        price: 68.00,
        originalPrice: 78.00,
        image: 'https://via.placeholder.com/200x200/74b9ff/ffffff?text=剁椒鱼头',
        categoryId: 3,
        categoryName: '湘菜',
        salesCount: 234,
        rating: 4.9,
        status: 1,
        isHot: true,
        isNew: false,
        isRecommended: true
      }
    ]
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },

  // 搜索确认
  onSearch() {
    console.log('搜索菜品:', this.data.searchKeyword)
    this.loadDishes(true)
  },

  // 分类筛选
  onCategoryFilter(e) {
    const category = e.currentTarget.dataset.category
    console.log('筛选分类:', category)
    
    this.setData({ selectedCategory: category })
    this.loadDishes(true)
  },

  // 添加菜品
  onAddDish() {
    console.log('添加菜品')
    wx.navigateTo({
      url: '/pages/admin/dish-edit/dish-edit?mode=add'
    })
  },

  // 编辑菜品
  onEditDish(e) {
    const dish = e.currentTarget.dataset.dish
    console.log('编辑菜品:', dish)
    
    wx.navigateTo({
      url: `/pages/admin/dish-edit/dish-edit?mode=edit&id=${dish.id}`
    })
  },

  // 切换状态
  onToggleStatus(e) {
    const dish = e.currentTarget.dataset.dish
    const newStatus = dish.status === 1 ? 0 : 1
    const statusText = newStatus === 1 ? '上架' : '下架'
    
    console.log('切换菜品状态:', dish.name, statusText)
    
    wx.showModal({
      title: '确认操作',
      content: `确定要${statusText}「${dish.name}」吗？`,
      success: (res) => {
        if (res.confirm) {
          this.updateDishStatus(dish.id, newStatus)
        }
      }
    })
  },

  // 删除菜品
  onDeleteDish(e) {
    const dish = e.currentTarget.dataset.dish
    console.log('删除菜品:', dish)
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除「${dish.name}」吗？删除后无法恢复。`,
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.deleteDish(dish.id)
        }
      }
    })
  },

  // 上传图片
  onUploadImage(e) {
    const dish = e.currentTarget.dataset.dish
    console.log('上传菜品图片:', dish)
    
    chooseAndUploadImage({
      category: 'dish',
      businessId: dish.id,
      description: `${dish.name}的图片`,
      count: 1
    }).then((image) => {
      console.log('图片上传成功:', image)
      
      // 更新菜品图片
      this.updateDishImage(dish.id, image.url)
      
    }).catch((error) => {
      console.error('图片上传失败:', error)
    })
  },

  // 预览图片
  onPreviewImage(e) {
    const url = e.currentTarget.dataset.url
    if (url) {
      previewImage(url)
    }
  },

  // 更新菜品状态
  async updateDishStatus(dishId, status) {
    try {
      // 这里应该调用后端API
      // await dishApi.updateDish(dishId, { status })
      
      // 模拟更新成功
      const dishes = this.data.dishes.map(dish => {
        if (dish.id === dishId) {
          return { ...dish, status }
        }
        return dish
      })
      
      this.setData({ dishes })
      
      wx.showToast({
        title: status === 1 ? '已上架' : '已下架',
        icon: 'success'
      })
      
    } catch (error) {
      console.error('更新菜品状态失败:', error)
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      })
    }
  },

  // 删除菜品
  async deleteDish(dishId) {
    try {
      // 这里应该调用后端API
      // await dishApi.deleteDish(dishId)
      
      // 模拟删除成功
      const dishes = this.data.dishes.filter(dish => dish.id !== dishId)
      this.setData({ dishes })
      
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })
      
    } catch (error) {
      console.error('删除菜品失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      })
    }
  },

  // 更新菜品图片
  async updateDishImage(dishId, imageUrl) {
    try {
      // 这里应该调用后端API
      // await dishApi.updateDish(dishId, { image: imageUrl })
      
      // 模拟更新成功
      const dishes = this.data.dishes.map(dish => {
        if (dish.id === dishId) {
          return { ...dish, image: imageUrl }
        }
        return dish
      })
      
      this.setData({ dishes })
      
      wx.showToast({
        title: '图片更新成功',
        icon: 'success'
      })
      
    } catch (error) {
      console.error('更新菜品图片失败:', error)
      wx.showToast({
        title: '图片更新失败',
        icon: 'none'
      })
    }
  }
})
