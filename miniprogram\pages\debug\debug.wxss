/* pages/debug/debug.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
  padding-bottom: 40rpx;
}

.debug-header {
  text-align: center;
  padding: 40rpx 0;
  color: white;
}

.debug-title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 15rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.debug-subtitle {
  font-size: 26rpx;
  opacity: 0.8;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

.debug-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 25rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.title-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #2f3542;
  flex: 1;
}

.clear-btn {
  font-size: 24rpx;
  color: #ff6b35;
  background: none;
  border: 1rpx solid #ff6b35;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
}

/* 信息网格 */
.info-grid, .data-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.info-item, .data-item {
  background: rgba(0, 0, 0, 0.03);
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
}

.info-label, .data-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.info-value, .data-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #2f3542;
}

.info-value.success, .data-value.success {
  color: #2ed573;
}

.info-value.error, .data-value.error {
  color: #ff4757;
}

/* 状态网格 */
.status-grid {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: rgba(0, 0, 0, 0.03);
  border-radius: 12rpx;
}

.status-label {
  font-size: 28rpx;
  color: #2f3542;
}

.status-indicator {
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.status-indicator.success {
  background: rgba(46, 213, 115, 0.1);
  color: #2ed573;
}

.status-indicator.error {
  background: rgba(255, 71, 87, 0.1);
  color: #ff4757;
}

.status-indicator.unknown {
  background: rgba(0, 0, 0, 0.1);
  color: #666;
}

/* 测试列表 */
.test-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.test-item {
  background: rgba(0, 0, 0, 0.03);
  border-radius: 12rpx;
  padding: 20rpx;
  transition: all 0.3s ease;
}

.test-item:active {
  background: rgba(0, 0, 0, 0.08);
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.test-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #2f3542;
}

.test-status {
  padding: 6rpx 12rpx;
  border-radius: 15rpx;
  font-size: 20rpx;
}

.test-status.success {
  background: rgba(46, 213, 115, 0.1);
}

.test-status.error {
  background: rgba(255, 71, 87, 0.1);
}

.status-icon {
  font-size: 24rpx;
}

.test-meta {
  display: flex;
  gap: 20rpx;
}

.test-duration {
  font-size: 24rpx;
  color: #2ed573;
}

.test-error {
  font-size: 24rpx;
  color: #ff4757;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 测试按钮 */
.test-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.test-btn {
  height: 80rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
}

.test-btn.danger {
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
}

/* 日志容器 */
.log-container {
  max-height: 400rpx;
  overflow-y: auto;
  background: rgba(0, 0, 0, 0.03);
  border-radius: 12rpx;
  padding: 20rpx;
}

.log-item {
  margin-bottom: 10rpx;
  padding: 10rpx;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 8rpx;
}

.log-item:last-child {
  margin-bottom: 0;
}

.log-text {
  font-size: 24rpx;
  color: #2f3542;
  font-family: monospace;
  line-height: 1.4;
}

/* 操作按钮 */
.debug-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

.action-btn {
  flex: 1;
  height: 100rpx;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  box-shadow: 0 8rpx 25rpx rgba(255, 107, 53, 0.4);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #ff6b35;
  border: 2rpx solid #ff6b35;
  backdrop-filter: blur(10rpx);
}

.action-btn:active {
  transform: translateY(2rpx);
}
