// pages/index/index.js
const { get } = require('../../utils/request')
const { showLoading, hideLoading, getImageUrl } = require('../../utils/util')

Page({
  data: {
    banners: [], // 轮播图
    categories: [], // 分类
    hotDishes: [], // 热门菜品
    newDishes: [], // 新品推荐
    loading: true,
    searchValue: '',
    hotKeywords: ['川菜', '粤菜', '湘菜', '素食', '甜品', '汤类'],
    cartCount: 0,
    totalPrice: 0
  },

  onLoad() {
    this.loadData()
  },

  onShow() {
    // 更新购物车数量
    this.updateCartCount()
  },

  onPullDownRefresh() {
    this.loadData().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载数据
  async loadData() {
    try {
      showLoading('加载中...')

      // 并行请求多个接口
      const [bannersRes, categoriesRes, hotDishesRes, newDishesRes] = await Promise.all([
        get('/banners'),
        get('/categories'),
        get('/dishes/hot'),
        get('/dishes/new')
      ])

      this.setData({
        banners: bannersRes.data || [],
        categories: categoriesRes.data || [],
        hotDishes: hotDishesRes.data || [],
        newDishes: newDishesRes.data || [],
        loading: false
      })
    } catch (error) {
      console.error('加载数据失败:', error)
      this.setData({
        loading: false
      })
    } finally {
      hideLoading()
    }
  },

  // 更新购物车数量
  updateCartCount() {
    const app = getApp()
    const cartData = app.getCartData()
    const cartCount = cartData.cart.reduce((total, item) => total + item.quantity, 0)
    const totalPrice = cartData.cart.reduce((total, item) => total + (item.price * item.quantity), 0)

    this.setData({
      cartCount,
      totalPrice
    })

    if (cartCount > 0) {
      wx.setTabBarBadge({
        index: 2,
        text: cartCount.toString()
      })
    } else {
      wx.removeTabBarBadge({
        index: 2
      })
    }
  },

  // 智能搜索
  onSearch(e) {
    const { keyword } = e.detail
    wx.navigateTo({
      url: `/pages/search/search?keyword=${encodeURIComponent(keyword)}`
    })
  },

  // 点击轮播图
  onBannerTap(e) {
    const { item } = e.currentTarget.dataset
    if (item.link_type === 'dish' && item.link_id) {
      wx.navigateTo({
        url: `/pages/detail/detail?id=${item.link_id}`
      })
    } else if (item.link_type === 'category' && item.link_id) {
      wx.navigateTo({
        url: `/pages/category/category?categoryId=${item.link_id}`
      })
    }
  },

  // 点击分类
  onCategoryTap(e) {
    const { item } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/category/category?categoryId=${item.id}`
    })
  },

  // 点击菜品
  onDishTap(e) {
    const { dish } = e.detail
    wx.navigateTo({
      url: `/pages/detail/detail?id=${dish.id}`
    })
  },

  // 添加到购物车
  onAddToCart(e) {
    const { dish } = e.detail
    const app = getApp()

    app.addToCart({
      id: dish.id,
      name: dish.name,
      price: dish.price,
      image: dish.image,
      quantity: 1
    })

    wx.showToast({
      title: '已添加到购物车',
      icon: 'success'
    })

    this.updateCartCount()
  },

  // 切换点赞
  onToggleLike(e) {
    const { dish, isLiked } = e.detail
    // 这里可以调用API保存用户的点赞状态
    console.log(`${isLiked ? '点赞' : '取消点赞'} 菜品:`, dish.name)
  },

  // 悬浮购物车点击
  onFloatingCartTap() {
    wx.switchTab({
      url: '/pages/cart/cart'
    })
  },

  // 悬浮购物车结算
  onFloatingCheckoutTap() {
    wx.switchTab({
      url: '/pages/cart/cart'
    })
  },

  // 查看更多热门菜品
  onMoreHotDishes() {
    wx.navigateTo({
      url: '/pages/category/category?type=hot'
    })
  },

  // 查看更多新品
  onMoreNewDishes() {
    wx.navigateTo({
      url: '/pages/category/category?type=new'
    })
  },

  // 获取图片URL
  getImageUrl(path) {
    return getImageUrl(path)
  }
})
