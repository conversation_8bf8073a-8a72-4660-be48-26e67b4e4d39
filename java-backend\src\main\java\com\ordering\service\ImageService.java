package com.ordering.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ordering.entity.Image;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 图片服务接口
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
public interface ImageService extends IService<Image> {

    /**
     * 上传单个图片
     * 
     * @param file 图片文件
     * @param category 图片分类
     * @param businessId 业务ID
     * @param description 图片描述
     * @return 图片信息
     */
    Image uploadImage(MultipartFile file, String category, Long businessId, String description);

    /**
     * 批量上传图片
     * 
     * @param files 图片文件列表
     * @param category 图片分类
     * @param businessId 业务ID
     * @param description 图片描述
     * @return 图片信息列表
     */
    List<Image> uploadImages(MultipartFile[] files, String category, Long businessId, String description);

    /**
     * 根据分类获取图片列表
     * 
     * @param category 图片分类
     * @param page 页码
     * @param size 每页大小
     * @return 分页图片列表
     */
    IPage<Image> getImagesByCategory(String category, int page, int size);

    /**
     * 根据业务ID获取图片列表
     * 
     * @param businessId 业务ID
     * @return 图片列表
     */
    List<Image> getImagesByBusinessId(Long businessId);

    /**
     * 根据分类和业务ID获取图片列表
     * 
     * @param category 图片分类
     * @param businessId 业务ID
     * @return 图片列表
     */
    List<Image> getImagesByCategoryAndBusinessId(String category, Long businessId);

    /**
     * 删除图片（物理删除）
     * 
     * @param id 图片ID
     * @return 是否删除成功
     */
    boolean deleteImage(Long id);

    /**
     * 批量删除图片
     * 
     * @param ids 图片ID列表
     * @return 删除成功的数量
     */
    int deleteImages(List<Long> ids);

    /**
     * 更新图片信息
     * 
     * @param id 图片ID
     * @param name 图片名称
     * @param description 图片描述
     * @param sortOrder 排序
     * @return 是否更新成功
     */
    boolean updateImageInfo(Long id, String name, String description, Integer sortOrder);

    /**
     * 获取图片详情
     * 
     * @param id 图片ID
     * @return 图片信息
     */
    Image getImageDetail(Long id);

    /**
     * 根据URL获取图片信息
     * 
     * @param url 图片URL
     * @return 图片信息
     */
    Image getImageByUrl(String url);

    /**
     * 压缩图片
     * 
     * @param sourceFile 源文件路径
     * @param targetFile 目标文件路径
     * @param quality 压缩质量
     * @param maxWidth 最大宽度
     * @param maxHeight 最大高度
     * @return 是否压缩成功
     */
    boolean compressImage(String sourceFile, String targetFile, double quality, int maxWidth, int maxHeight);

    /**
     * 生成缩略图
     * 
     * @param sourceFile 源文件路径
     * @param thumbnailFile 缩略图文件路径
     * @param width 缩略图宽度
     * @param height 缩略图高度
     * @return 是否生成成功
     */
    boolean generateThumbnail(String sourceFile, String thumbnailFile, int width, int height);

    /**
     * 验证文件类型
     * 
     * @param file 文件
     * @return 是否为允许的图片类型
     */
    boolean isValidImageType(MultipartFile file);

    /**
     * 验证文件大小
     * 
     * @param file 文件
     * @return 是否在允许的大小范围内
     */
    boolean isValidFileSize(MultipartFile file);

    /**
     * 生成唯一文件名
     * 
     * @param originalFilename 原始文件名
     * @return 唯一文件名
     */
    String generateUniqueFileName(String originalFilename);

    /**
     * 获取文件扩展名
     * 
     * @param filename 文件名
     * @return 扩展名
     */
    String getFileExtension(String filename);
}
