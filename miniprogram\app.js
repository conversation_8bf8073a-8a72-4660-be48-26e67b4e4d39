// app.js
const { themeManager } = require('./utils/theme')

App({
  globalData: {
    userInfo: null,
    token: null,
    baseUrl: 'http://localhost:3000/api',
    cart: [],
    systemInfo: {},
    networkType: '',
    isConnected: true,
    currentTheme: 'default'
  },

  onLaunch(options) {
    console.log('小程序启动', options)
    
    // 初始化系统信息
    this.initSystemInfo()
    
    // 检查网络状态
    this.checkNetworkStatus()
    
    // 检查登录状态
    this.checkLoginStatus()
    
    // 加载购物车数据
    this.loadCartData()
    
    // 初始化主题
    this.initTheme()
    
    // 监听网络状态变化
    this.watchNetworkStatus()
    
    // 检查更新
    this.checkForUpdate()
  },

  onShow(options) {
    console.log('小程序显示', options)
    
    // 更新网络状态
    this.checkNetworkStatus()
  },

  onHide() {
    console.log('小程序隐藏')
  },

  onError(error) {
    console.error('小程序错误:', error)
    
    // 错误上报
    this.reportError(error)
  },

  // 初始化系统信息
  initSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res
        console.log('系统信息:', res)
      },
      fail: (error) => {
        console.error('获取系统信息失败:', error)
      }
    })
  },

  // 检查网络状态
  checkNetworkStatus() {
    wx.getNetworkType({
      success: (res) => {
        this.globalData.networkType = res.networkType
        this.globalData.isConnected = res.networkType !== 'none'
        
        if (!this.globalData.isConnected) {
          wx.showToast({
            title: '网络连接异常',
            icon: 'none'
          })
        }
      }
    })
  },

  // 监听网络状态变化
  watchNetworkStatus() {
    wx.onNetworkStatusChange((res) => {
      this.globalData.isConnected = res.isConnected
      this.globalData.networkType = res.networkType
      
      if (!res.isConnected) {
        wx.showToast({
          title: '网络连接断开',
          icon: 'none'
        })
      } else {
        wx.showToast({
          title: '网络已连接',
          icon: 'success'
        })
      }
    })
  },

  // 检查更新
  checkForUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          console.log('发现新版本')
        }
      })
      
      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })
      
      updateManager.onUpdateFailed(() => {
        console.error('新版本下载失败')
      })
    }
  },

  // 初始化主题
  initTheme() {
    themeManager.loadTheme()
    this.globalData.currentTheme = themeManager.getCurrentTheme().name
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token')
    const userInfo = wx.getStorageSync('userInfo')
    
    if (token && userInfo) {
      this.globalData.token = token
      this.globalData.userInfo = userInfo
      console.log('用户已登录:', userInfo)
    } else {
      console.log('用户未登录')
    }
  },

  // 加载购物车数据
  loadCartData() {
    try {
      const cart = wx.getStorageSync('cart') || []
      this.globalData.cart = cart
      console.log('购物车数据加载完成:', cart.length, '件商品')
    } catch (error) {
      console.error('加载购物车数据失败:', error)
      this.globalData.cart = []
    }
  },

  // 保存购物车数据
  saveCartData() {
    try {
      wx.setStorageSync('cart', this.globalData.cart)
      
      // 触发购物车更新事件
      this.triggerCartUpdate()
    } catch (error) {
      console.error('保存购物车数据失败:', error)
    }
  },

  // 触发购物车更新事件
  triggerCartUpdate() {
    // 可以在这里添加全局事件通知
    const cartData = this.getCartData()
    
    // 更新TabBar徽章
    if (cartData.totalCount > 0) {
      wx.setTabBarBadge({
        index: 2,
        text: cartData.totalCount.toString()
      })
    } else {
      wx.removeTabBarBadge({
        index: 2
      })
    }
  },

  // 添加到购物车
  addToCart(item) {
    if (!item || !item.id) {
      console.error('添加到购物车失败: 商品信息不完整')
      return false
    }
    
    const existingItem = this.globalData.cart.find(cartItem => cartItem.id === item.id)
    
    if (existingItem) {
      existingItem.quantity += item.quantity || 1
    } else {
      this.globalData.cart.push({
        id: item.id,
        name: item.name,
        price: parseFloat(item.price),
        image: item.image,
        quantity: item.quantity || 1,
        addTime: Date.now()
      })
    }
    
    this.saveCartData()
    console.log('商品已添加到购物车:', item.name)
    return true
  },

  // 更新购物车商品数量
  updateCartItem(id, quantity) {
    const item = this.globalData.cart.find(cartItem => cartItem.id === id)
    if (item) {
      if (quantity <= 0) {
        this.removeFromCart(id)
      } else {
        item.quantity = quantity
        this.saveCartData()
        console.log('购物车商品数量已更新:', item.name, quantity)
      }
      return true
    }
    return false
  },

  // 从购物车移除
  removeFromCart(id) {
    const originalLength = this.globalData.cart.length
    this.globalData.cart = this.globalData.cart.filter(item => item.id !== id)
    
    if (this.globalData.cart.length < originalLength) {
      this.saveCartData()
      console.log('商品已从购物车移除:', id)
      return true
    }
    return false
  },

  // 批量删除购物车商品
  removeMultipleFromCart(ids) {
    if (!Array.isArray(ids) || ids.length === 0) return false
    
    const originalLength = this.globalData.cart.length
    this.globalData.cart = this.globalData.cart.filter(item => !ids.includes(item.id))
    
    if (this.globalData.cart.length < originalLength) {
      this.saveCartData()
      console.log('批量删除购物车商品:', ids.length, '件')
      return true
    }
    return false
  },

  // 清空购物车
  clearCart() {
    this.globalData.cart = []
    this.saveCartData()
    console.log('购物车已清空')
  },

  // 获取购物车数据
  getCartData() {
    const cart = this.globalData.cart
    const totalPrice = cart.reduce((total, item) => total + (item.price * item.quantity), 0)
    const totalCount = cart.reduce((total, item) => total + item.quantity, 0)
    
    return {
      cart,
      totalPrice: parseFloat(totalPrice.toFixed(2)),
      totalCount
    }
  },

  // 设置用户信息
  setUserInfo(userInfo) {
    this.globalData.userInfo = userInfo
    try {
      wx.setStorageSync('userInfo', userInfo)
      console.log('用户信息已保存:', userInfo)
    } catch (error) {
      console.error('保存用户信息失败:', error)
    }
  },

  // 获取用户信息
  getUserInfo() {
    return this.globalData.userInfo
  },

  // 设置Token
  setToken(token) {
    this.globalData.token = token
    try {
      wx.setStorageSync('token', token)
      console.log('Token已保存')
    } catch (error) {
      console.error('保存Token失败:', error)
    }
  },

  // 获取Token
  getToken() {
    return this.globalData.token
  },

  // 清除用户信息
  clearUserInfo() {
    this.globalData.userInfo = null
    this.globalData.token = null
    
    try {
      wx.removeStorageSync('userInfo')
      wx.removeStorageSync('token')
      console.log('用户信息已清除')
    } catch (error) {
      console.error('清除用户信息失败:', error)
    }
  },

  // 错误上报
  reportError(error) {
    // 这里可以添加错误上报逻辑
    console.error('应用错误:', error)
    
    // 可以发送到后端进行错误统计
    // this.request('/errors/report', { error: error.toString() })
  },

  // 获取系统信息
  getSystemInfo() {
    return this.globalData.systemInfo
  },

  // 检查网络连接
  isNetworkConnected() {
    return this.globalData.isConnected
  }
})
