<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <view class="user-bg"></view>
    <view class="user-content">
      <!-- 已登录状态 -->
      <view class="user-info" wx:if="{{userInfo}}">
        <image 
          class="user-avatar" 
          src="{{userInfo.avatar || '/images/default-avatar.png'}}" 
          mode="aspectFill"
          bindtap="onAvatarTap"
        />
        <view class="user-details">
          <text class="user-name">{{userInfo.nickname || userInfo.phone}}</text>
          <text class="user-phone">{{userInfo.phone}}</text>
        </view>
        <button class="logout-btn" bindtap="onLogout">退出</button>
      </view>
      
      <!-- 未登录状态 -->
      <view class="login-prompt" wx:else>
        <image 
          class="default-avatar" 
          src="/images/default-avatar.png" 
          mode="aspectFill"
          bindtap="onAvatarTap"
        />
        <view class="login-info">
          <text class="login-title">登录享受更多服务</text>
          <text class="login-subtitle">查看订单、收藏菜品、专属优惠</text>
        </view>
        <button class="login-btn" bindtap="onLogin">立即登录</button>
      </view>
    </view>
  </view>

  <!-- 订单统计 -->
  <view class="order-stats" wx:if="{{userInfo}}">
    <view class="stats-header" bindtap="onMyOrders">
      <text class="stats-title">我的订单</text>
      <text class="stats-more">查看全部 ></text>
    </view>
    <view class="stats-grid">
      <view 
        class="stats-item" 
        bindtap="onOrderStatusTap"
        data-status="pending"
      >
        <text class="stats-count">{{orderStats.pending}}</text>
        <text class="stats-label">待确认</text>
      </view>
      <view 
        class="stats-item" 
        bindtap="onOrderStatusTap"
        data-status="confirmed"
      >
        <text class="stats-count">{{orderStats.confirmed}}</text>
        <text class="stats-label">已确认</text>
      </view>
      <view 
        class="stats-item" 
        bindtap="onOrderStatusTap"
        data-status="preparing"
      >
        <text class="stats-count">{{orderStats.preparing}}</text>
        <text class="stats-label">制作中</text>
      </view>
      <view 
        class="stats-item" 
        bindtap="onOrderStatusTap"
        data-status="completed"
      >
        <text class="stats-count">{{orderStats.completed}}</text>
        <text class="stats-label">已完成</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-group">
      <view class="menu-item" bindtap="onMyFavorites">
        <text class="menu-icon">❤️</text>
        <text class="menu-text">我的收藏</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" bindtap="onMyAddress">
        <text class="menu-icon">📍</text>
        <text class="menu-text">收货地址</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" bindtap="onMyCoupons">
        <text class="menu-icon">🎫</text>
        <text class="menu-text">优惠券</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <view class="menu-group">
      <view class="menu-item" bindtap="onCustomerService">
        <text class="menu-icon">📞</text>
        <text class="menu-text">联系客服</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" bindtap="onSettings">
        <text class="menu-icon">⚙️</text>
        <text class="menu-text">设置</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" bindtap="onAbout">
        <text class="menu-icon">ℹ️</text>
        <text class="menu-text">关于我们</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" bindtap="onDebug" wx:if="{{showDebug}}">
        <text class="menu-icon">🔧</text>
        <text class="menu-text">系统调试</text>
        <text class="menu-arrow">></text>
      </view>
    </view>
  </view>
</view>
