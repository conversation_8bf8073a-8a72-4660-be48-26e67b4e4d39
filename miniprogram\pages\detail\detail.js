// pages/detail/detail.js
const { get } = require('../../utils/request')
const { showLoading, hideLoading, showToast } = require('../../utils/util')

Page({
  data: {
    dish: null,
    loading: true,
    quantity: 1,
    selectedSpecs: {},
    totalPrice: 0,
    isLiked: false,
    relatedDishes: [],
    cartCount: 0,
    showSpecModal: false,
    currentImageIndex: 0,
    imageList: []
  },

  onLoad(options) {
    const { id } = options
    if (id) {
      this.dishId = id
      this.loadDishDetail()
      this.loadRelatedDishes()
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  onShow() {
    this.updateCartCount()
  },

  // 加载菜品详情
  async loadDishDetail() {
    try {
      showLoading('加载中...')
      
      const res = await get(`/dishes/${this.dishId}`)
      const dish = res.data
      
      // 处理图片列表
      const imageList = dish.images ? dish.images.split(',') : [dish.image]
      
      this.setData({
        dish,
        loading: false,
        totalPrice: parseFloat(dish.price),
        imageList,
        currentImageIndex: 0
      })
      
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: dish.name
      })
      
    } catch (error) {
      console.error('加载菜品详情失败:', error)
      this.setData({ loading: false })
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      hideLoading()
    }
  },

  // 加载相关菜品
  async loadRelatedDishes() {
    try {
      const res = await get('/dishes', {
        category_id: this.data.dish?.category_id,
        limit: 6
      })
      
      // 过滤掉当前菜品
      const relatedDishes = res.data.list.filter(item => item.id != this.dishId)
      
      this.setData({
        relatedDishes: relatedDishes.slice(0, 4)
      })
      
    } catch (error) {
      console.error('加载相关菜品失败:', error)
    }
  },

  // 图片点击预览
  onImageTap(e) {
    const { index } = e.currentTarget.dataset
    
    wx.previewImage({
      current: this.data.imageList[index],
      urls: this.data.imageList
    })
  },

  // 图片轮播变化
  onSwiperChange(e) {
    this.setData({
      currentImageIndex: e.detail.current
    })
  },

  // 数量减少
  onQuantityDecrease() {
    if (this.data.quantity > 1) {
      const quantity = this.data.quantity - 1
      this.setData({
        quantity,
        totalPrice: parseFloat((this.data.dish.price * quantity).toFixed(2))
      })
    }
  },

  // 数量增加
  onQuantityIncrease() {
    const quantity = this.data.quantity + 1
    this.setData({
      quantity,
      totalPrice: parseFloat((this.data.dish.price * quantity).toFixed(2))
    })
  },

  // 数量输入
  onQuantityInput(e) {
    let quantity = parseInt(e.detail.value) || 1
    if (quantity < 1) quantity = 1
    if (quantity > 99) quantity = 99
    
    this.setData({
      quantity,
      totalPrice: parseFloat((this.data.dish.price * quantity).toFixed(2))
    })
  },

  // 切换收藏状态
  onToggleLike() {
    const isLiked = !this.data.isLiked
    this.setData({ isLiked })
    
    // 这里可以调用API保存收藏状态
    showToast(isLiked ? '已添加到收藏' : '已取消收藏', 'success')
  },

  // 添加到购物车
  onAddToCart() {
    const { dish, quantity } = this.data
    const app = getApp()
    
    const success = app.addToCart({
      id: dish.id,
      name: dish.name,
      price: dish.price,
      image: dish.image,
      quantity
    })
    
    if (success) {
      showToast('已添加到购物车', 'success')
      this.updateCartCount()
      
      // 重置数量
      this.setData({
        quantity: 1,
        totalPrice: parseFloat(dish.price)
      })
    }
  },

  // 立即购买
  onBuyNow() {
    const { dish, quantity } = this.data
    const app = getApp()
    
    // 清空购物车并添加当前商品
    app.clearCart()
    app.addToCart({
      id: dish.id,
      name: dish.name,
      price: dish.price,
      image: dish.image,
      quantity
    })
    
    // 跳转到订单确认页面
    wx.navigateTo({
      url: '/pages/order/order'
    })
  },

  // 查看购物车
  onViewCart() {
    wx.switchTab({
      url: '/pages/cart/cart'
    })
  },

  // 相关菜品点击
  onRelatedDishTap(e) {
    const { dish } = e.currentTarget.dataset
    
    // 重新加载页面数据
    this.dishId = dish.id
    this.setData({
      loading: true,
      quantity: 1,
      currentImageIndex: 0
    })
    
    this.loadDishDetail()
    this.loadRelatedDishes()
  },

  // 更新购物车数量
  updateCartCount() {
    const app = getApp()
    const cartData = app.getCartData()
    
    this.setData({
      cartCount: cartData.totalCount
    })
    
    if (cartData.totalCount > 0) {
      wx.setTabBarBadge({
        index: 2,
        text: cartData.totalCount.toString()
      })
    } else {
      wx.removeTabBarBadge({
        index: 2
      })
    }
  },

  // 分享
  onShareAppMessage() {
    const { dish } = this.data
    return {
      title: `推荐美食：${dish?.name}`,
      path: `/pages/detail/detail?id=${this.dishId}`,
      imageUrl: dish?.image
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    const { dish } = this.data
    return {
      title: `推荐美食：${dish?.name}`,
      imageUrl: dish?.image
    }
  },

  // 返回上一页
  onBack() {
    wx.navigateBack()
  }
})
