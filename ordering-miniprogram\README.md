# 🍽️ 在线点餐小程序

一个功能完整的微信小程序在线点餐系统，支持菜品浏览、购物车管理、订单处理、图片上传等功能，并与Java后端完美集成。

## ✨ 功能特色

### 🛍️ 用户端功能
- **首页展示**: 轮播图、分类导航、推荐菜品、热门菜品
- **菜品浏览**: 分类筛选、搜索功能、菜品详情
- **购物车管理**: 添加商品、数量调整、费用计算
- **订单处理**: 地址选择、优惠券使用、订单确认
- **用户中心**: 个人信息、订单历史、地址管理

### 👨‍💼 管理员功能
- **菜品管理**: 添加、编辑、删除菜品
- **图片上传**: 连接Java后端图片服务
- **订单管理**: 查看和处理订单
- **数据统计**: 销售数据、用户统计

### 🔧 技术特性
- **现代化UI**: 精美的界面设计，流畅的用户体验
- **响应式布局**: 适配不同屏幕尺寸
- **状态管理**: 全局购物车状态管理
- **网络请求**: 统一的API请求封装
- **图片处理**: 完整的图片上传和管理功能
- **错误处理**: 完善的异常处理机制

## 🏗️ 项目结构

```
ordering-miniprogram/
├── app.js                    # 全局应用逻辑
├── app.json                  # 全局配置
├── app.wxss                  # 全局样式
├── pages/                    # 页面目录
│   ├── index/               # 首页
│   │   ├── index.js
│   │   ├── index.wxml
│   │   ├── index.wxss
│   │   └── index.json
│   ├── menu/                # 菜单页面
│   ├── cart/                # 购物车页面
│   ├── profile/             # 个人中心
│   └── admin/               # 管理员页面
│       ├── dish-manage/     # 菜品管理
│       └── order-manage/    # 订单管理
├── utils/                   # 工具类
│   ├── request.js          # 网络请求
│   ├── imageUpload.js      # 图片上传
│   └── api.js              # API接口
├── images/                  # 图片资源
├── project.config.json     # 项目配置
├── sitemap.json           # 站点地图
└── README.md              # 项目说明
```

## 🚀 快速开始

### 环境要求
- **微信开发者工具**: 最新稳定版
- **Java后端**: 确保Java后端服务已启动（端口8080）
- **网络配置**: 开发时需要开启"不校验合法域名"

### 1. 导入项目
1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择 `ordering-miniprogram` 目录
4. 填写AppID（测试可使用测试号）

### 2. 配置后端地址
编辑 `utils/request.js` 中的baseUrl：
```javascript
const config = {
  baseUrl: 'http://localhost:8080/api',  // Java后端地址
  // ...其他配置
}
```

### 3. 启动项目
1. 确保Java后端服务已启动
2. 在微信开发者工具中点击"编译"
3. 预览小程序效果

## 📱 页面功能

### 首页 (pages/index)
- **轮播图展示**: 支持点击跳转
- **分类导航**: 快速筛选菜品
- **推荐菜品**: 网格布局展示
- **热门菜品**: 列表形式展示
- **新品菜品**: 横向滚动展示

### 菜单页面 (pages/menu)
- **左右布局**: 左侧分类，右侧菜品
- **搜索功能**: 实时搜索菜品
- **数量控制**: 购物车数量调整
- **购物车栏**: 实时显示总价和数量

### 购物车 (pages/cart)
- **商品列表**: 显示已选商品
- **数量调整**: 增减商品数量
- **费用计算**: 小计、配送费、优惠券
- **地址选择**: 配送地址管理
- **订单备注**: 特殊需求说明

### 个人中心 (pages/profile)
- **用户信息**: 头像、昵称、等级
- **订单统计**: 各状态订单数量
- **功能菜单**: 订单、地址、优惠券等
- **管理功能**: 管理员专用功能

### 管理员页面 (pages/admin)
- **菜品管理**: CRUD操作、图片上传
- **订单管理**: 订单状态管理
- **数据统计**: 销售和用户数据

## 🔗 与Java后端集成

### API接口对接
小程序通过以下方式与Java后端通信：

```javascript
// 图片上传示例
const { chooseAndUploadImage } = require('../../utils/imageUpload')

chooseAndUploadImage({
  category: 'dish',
  businessId: 1,
  description: '菜品图片'
}).then((image) => {
  console.log('上传成功:', image.url)
})
```

### 主要API接口
- `POST /api/images/upload` - 图片上传
- `GET /api/images/category/{category}` - 获取分类图片
- `GET /api/dishes` - 获取菜品列表
- `POST /api/orders` - 创建订单
- `GET /api/users/{id}` - 获取用户信息

### 数据流转
1. **图片上传**: 小程序 → Java后端 → 返回图片URL
2. **菜品管理**: 管理员上传图片 → 创建/编辑菜品
3. **订单处理**: 用户下单 → 后端处理 → 状态更新

## 🎨 UI设计特色

### 设计风格
- **现代简约**: 清爽的界面设计
- **橙色主题**: 温暖的美食色调
- **圆角设计**: 友好的视觉体验
- **渐变效果**: 丰富的视觉层次

### 交互体验
- **流畅动画**: 页面切换和状态变化
- **即时反馈**: 操作结果实时提示
- **手势支持**: 滑动、点击等手势
- **加载状态**: 完善的加载提示

### 响应式适配
- **多屏适配**: 支持不同屏幕尺寸
- **安全区域**: 适配刘海屏等特殊屏幕
- **横竖屏**: 自适应屏幕方向

## 🔧 开发指南

### 添加新页面
1. 在 `pages` 目录下创建页面文件夹
2. 创建 `.js`, `.wxml`, `.wxss`, `.json` 文件
3. 在 `app.json` 中注册页面路径

### 调用API接口
```javascript
const { dishApi } = require('../../utils/api')

// 获取菜品列表
dishApi.getDishList({ page: 1, size: 10 })
  .then(data => {
    console.log('菜品数据:', data)
  })
  .catch(error => {
    console.error('请求失败:', error)
  })
```

### 图片上传处理
```javascript
const { chooseAndUploadImage } = require('../../utils/imageUpload')

// 选择并上传图片
chooseAndUploadImage({
  category: 'dish',      // 图片分类
  businessId: dishId,    // 关联业务ID
  description: '菜品图片', // 图片描述
  count: 1              // 选择数量
}).then(image => {
  // 处理上传成功
}).catch(error => {
  // 处理上传失败
})
```

### 状态管理
```javascript
const app = getApp()

// 添加到购物车
app.addToCart(dish, quantity)

// 获取购物车数量
const cartCount = app.getCartItemCount()

// 清空购物车
app.clearCart()
```

## 📊 性能优化

### 图片优化
- **懒加载**: 图片按需加载
- **压缩处理**: 后端自动压缩
- **缩略图**: 列表使用缩略图
- **缓存策略**: 图片缓存机制

### 数据优化
- **分页加载**: 大数据量分页处理
- **本地缓存**: 常用数据本地存储
- **请求合并**: 减少网络请求
- **状态复用**: 页面状态保持

### 代码优化
- **组件化**: 可复用组件设计
- **工具函数**: 通用功能封装
- **异常处理**: 完善的错误处理
- **代码分割**: 按需加载代码

## 🔒 安全考虑

### 数据安全
- **输入验证**: 前端数据校验
- **XSS防护**: 内容安全过滤
- **权限控制**: 管理员功能限制
- **敏感信息**: 避免敏感数据泄露

### 网络安全
- **HTTPS**: 生产环境使用HTTPS
- **域名校验**: 合法域名配置
- **请求签名**: API请求安全验证
- **错误处理**: 避免错误信息泄露

## 📞 技术支持

### 常见问题
1. **网络请求失败**: 检查后端服务是否启动
2. **图片上传失败**: 确认后端图片服务正常
3. **页面显示异常**: 检查数据格式和组件状态
4. **功能无响应**: 查看控制台错误信息

### 调试技巧
- 使用微信开发者工具的调试功能
- 查看Network面板的网络请求
- 使用console.log输出调试信息
- 检查Storage中的本地数据

---

✅ **完整的在线点餐小程序已创建完成，可以与Java后端完美配合使用！**
