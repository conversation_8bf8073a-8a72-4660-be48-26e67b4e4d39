// pages/index/index.js
const app = getApp()
const { bannerApi, categoryApi, dishApi } = require('../../utils/api')

Page({
  data: {
    // 页面状态
    loading: true,
    
    // 轮播图数据
    banners: [],
    
    // 分类数据
    categories: [],
    selectedCategory: null,
    
    // 菜品数据
    recommendDishes: [],
    hotDishes: [],
    newDishes: [],
    
    // 购物车数量
    cartCount: 0
  },

  onLoad() {
    console.log('首页加载')
    this.initPage()
  },

  onShow() {
    console.log('首页显示')
    this.updateCartCount()
  },

  onPullDownRefresh() {
    console.log('下拉刷新')
    this.initPage().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    console.log('触底加载')
    // 可以在这里加载更多数据
  },

  // 初始化页面
  async initPage() {
    this.setData({ loading: true })
    
    try {
      await Promise.all([
        this.loadBanners(),
        this.loadCategories(),
        this.loadRecommendDishes(),
        this.loadHotDishes(),
        this.loadNewDishes()
      ])
    } catch (error) {
      console.error('页面初始化失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载轮播图
  async loadBanners() {
    try {
      // 模拟轮播图数据（实际应该从后端获取）
      const banners = [
        {
          id: 1,
          title: '新品上市',
          image: 'https://via.placeholder.com/750x320/ff6b35/ffffff?text=新品上市',
          linkType: 'category',
          linkValue: '1'
        },
        {
          id: 2,
          title: '限时优惠',
          image: 'https://via.placeholder.com/750x320/f7931e/ffffff?text=限时优惠',
          linkType: 'dish',
          linkValue: '1'
        },
        {
          id: 3,
          title: '招牌菜品',
          image: 'https://via.placeholder.com/750x320/2ecc71/ffffff?text=招牌菜品',
          linkType: 'category',
          linkValue: '2'
        }
      ]
      
      this.setData({ banners })
    } catch (error) {
      console.error('加载轮播图失败:', error)
    }
  },

  // 加载分类
  async loadCategories() {
    try {
      // 模拟分类数据
      const categories = [
        {
          id: 1,
          name: '川菜',
          icon: 'https://via.placeholder.com/80x80/ff6b35/ffffff?text=川'
        },
        {
          id: 2,
          name: '粤菜',
          icon: 'https://via.placeholder.com/80x80/f7931e/ffffff?text=粤'
        },
        {
          id: 3,
          name: '湘菜',
          icon: 'https://via.placeholder.com/80x80/2ecc71/ffffff?text=湘'
        },
        {
          id: 4,
          name: '鲁菜',
          icon: 'https://via.placeholder.com/80x80/74b9ff/ffffff?text=鲁'
        },
        {
          id: 5,
          name: '苏菜',
          icon: 'https://via.placeholder.com/80x80/a29bfe/ffffff?text=苏'
        },
        {
          id: 6,
          name: '浙菜',
          icon: 'https://via.placeholder.com/80x80/fd79a8/ffffff?text=浙'
        }
      ]
      
      this.setData({ categories })
    } catch (error) {
      console.error('加载分类失败:', error)
    }
  },

  // 加载推荐菜品
  async loadRecommendDishes() {
    try {
      // 模拟推荐菜品数据
      const recommendDishes = [
        {
          id: 1,
          name: '麻婆豆腐',
          description: '经典川菜，麻辣鲜香',
          price: 28.00,
          image: 'https://via.placeholder.com/300x240/ff6b35/ffffff?text=麻婆豆腐',
          isHot: true,
          isNew: false,
          categoryId: 1
        },
        {
          id: 2,
          name: '宫保鸡丁',
          description: '传统川菜，酸甜微辣',
          price: 32.00,
          image: 'https://via.placeholder.com/300x240/f7931e/ffffff?text=宫保鸡丁',
          isHot: false,
          isNew: true,
          categoryId: 1
        },
        {
          id: 3,
          name: '白切鸡',
          description: '经典粤菜，清淡鲜美',
          price: 45.00,
          image: 'https://via.placeholder.com/300x240/2ecc71/ffffff?text=白切鸡',
          isHot: false,
          isNew: false,
          categoryId: 2
        },
        {
          id: 4,
          name: '剁椒鱼头',
          description: '湘菜名菜，香辣开胃',
          price: 68.00,
          image: 'https://via.placeholder.com/300x240/74b9ff/ffffff?text=剁椒鱼头',
          isHot: true,
          isNew: false,
          categoryId: 3
        }
      ]
      
      this.setData({ recommendDishes })
    } catch (error) {
      console.error('加载推荐菜品失败:', error)
    }
  },

  // 加载热门菜品
  async loadHotDishes() {
    try {
      // 模拟热门菜品数据
      const hotDishes = [
        {
          id: 1,
          name: '麻婆豆腐',
          description: '经典川菜，麻辣鲜香，嫩滑爽口',
          price: 28.00,
          image: 'https://via.placeholder.com/120x120/ff6b35/ffffff?text=麻婆豆腐',
          salesCount: 156,
          rating: 4.8
        },
        {
          id: 4,
          name: '剁椒鱼头',
          description: '湘菜名菜，香辣开胃，鱼肉鲜美',
          price: 68.00,
          image: 'https://via.placeholder.com/120x120/74b9ff/ffffff?text=剁椒鱼头',
          salesCount: 234,
          rating: 4.9
        },
        {
          id: 2,
          name: '宫保鸡丁',
          description: '传统川菜，酸甜微辣，鸡肉嫩滑',
          price: 32.00,
          image: 'https://via.placeholder.com/120x120/f7931e/ffffff?text=宫保鸡丁',
          salesCount: 89,
          rating: 4.7
        }
      ]
      
      this.setData({ hotDishes })
    } catch (error) {
      console.error('加载热门菜品失败:', error)
    }
  },

  // 加载新品菜品
  async loadNewDishes() {
    try {
      // 模拟新品菜品数据
      const newDishes = [
        {
          id: 2,
          name: '宫保鸡丁',
          price: 32.00,
          image: 'https://via.placeholder.com/240x180/f7931e/ffffff?text=宫保鸡丁'
        },
        {
          id: 5,
          name: '糖醋里脊',
          price: 36.00,
          image: 'https://via.placeholder.com/240x180/a29bfe/ffffff?text=糖醋里脊'
        },
        {
          id: 6,
          name: '红烧肉',
          price: 42.00,
          image: 'https://via.placeholder.com/240x180/fd79a8/ffffff?text=红烧肉'
        }
      ]
      
      this.setData({ newDishes })
    } catch (error) {
      console.error('加载新品菜品失败:', error)
    }
  },

  // 更新购物车数量
  updateCartCount() {
    const cartCount = app.getCartItemCount()
    this.setData({ cartCount })
  },

  // 搜索点击
  onSearchTap() {
    wx.navigateTo({
      url: '/pages/search/search'
    })
  },

  // 轮播图点击
  onBannerTap(e) {
    const banner = e.currentTarget.dataset.banner
    console.log('轮播图点击:', banner)
    
    switch (banner.linkType) {
      case 'dish':
        wx.navigateTo({
          url: `/pages/dish-detail/dish-detail?id=${banner.linkValue}`
        })
        break
      case 'category':
        wx.switchTab({
          url: '/pages/menu/menu'
        })
        break
      case 'url':
        // 处理外部链接
        break
    }
  },

  // 分类点击
  onCategoryTap(e) {
    const category = e.currentTarget.dataset.category
    console.log('分类点击:', category)
    
    this.setData({ selectedCategory: category.id })
    
    // 跳转到菜单页面并传递分类ID
    wx.switchTab({
      url: '/pages/menu/menu'
    })
  },

  // 菜品点击
  onDishTap(e) {
    const dish = e.currentTarget.dataset.dish
    console.log('菜品点击:', dish)
    
    wx.navigateTo({
      url: `/pages/dish-detail/dish-detail?id=${dish.id}`
    })
  },

  // 添加到购物车
  onAddToCartTap(e) {
    e.stopPropagation() // 阻止事件冒泡
    
    const dish = e.currentTarget.dataset.dish
    console.log('添加到购物车:', dish)
    
    app.addToCart(dish, 1)
    this.updateCartCount()
  },

  // 更多推荐
  onMoreRecommendTap() {
    wx.switchTab({
      url: '/pages/menu/menu'
    })
  },

  // 更多热门
  onMoreHotTap() {
    wx.switchTab({
      url: '/pages/menu/menu'
    })
  },

  // 购物车点击
  onCartTap() {
    wx.switchTab({
      url: '/pages/cart/cart'
    })
  }
})
