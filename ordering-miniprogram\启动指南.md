# 🚀 微信小程序启动指南

## 📋 问题诊断

如果小程序没有反应，请按以下步骤检查：

### 1. 检查微信开发者工具设置

#### ✅ 基本设置
- 打开微信开发者工具
- 确保已登录微信开发者账号
- 检查工具版本是否为最新稳定版

#### ✅ 项目设置
- 项目根目录：`ordering-miniprogram`
- AppID：可以使用测试号或真实AppID
- 项目名称：美食点餐小程序

### 2. 检查开发设置

#### ✅ 本地设置
在微信开发者工具中，点击右上角"详情"：
- ✅ 勾选"不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"
- ✅ 勾选"开启调试模式"
- ✅ 基础库版本选择 2.19.4 或更高

#### ✅ 编译设置
- 点击"编译"按钮
- 如果有错误，查看控制台输出
- 确保所有页面文件都存在

### 3. 常见问题解决

#### 🔧 问题1：页面空白
**原因**：缺少图片资源或页面文件
**解决**：
```
1. 检查 images/tab/ 目录下是否有图标文件
2. 如果缺少，可以暂时使用任意 81x81px 的PNG图片
3. 重新编译项目
```

#### 🔧 问题2：底部导航不显示
**原因**：tabBar配置中的图片路径错误
**解决**：
```
1. 确保 images/tab/ 目录存在
2. 放置对应的图标文件：
   - home.png / home-active.png
   - menu.png / menu-active.png  
   - cart.png / cart-active.png
   - profile.png / profile-active.png
3. 重新编译
```

#### 🔧 问题3：功能无响应
**原因**：JavaScript错误或网络请求失败
**解决**：
```
1. 打开控制台查看错误信息
2. 检查网络请求是否正常
3. 确保Java后端服务已启动（localhost:8080）
```

### 4. 快速测试步骤

#### 📱 测试小程序基本功能
1. **启动测试**
   - 打开首页，应该看到橙色的测试区域
   - 点击"测试功能"按钮
   - 应该弹出"小程序运行正常！"的提示

2. **导航测试**
   - 点击底部导航的各个标签
   - 确保能正常切换页面

3. **功能测试**
   - 首页：查看轮播图和推荐菜品
   - 菜单：浏览菜品列表
   - 购物车：查看购物车状态
   - 个人中心：查看用户信息

### 5. 图片资源说明

#### 📸 必需的图片文件
```
images/tab/
├── home.png (81x81px)
├── home-active.png (81x81px)
├── menu.png (81x81px)
├── menu-active.png (81x81px)
├── cart.png (81x81px)
├── cart-active.png (81x81px)
├── profile.png (81x81px)
└── profile-active.png (81x81px)

images/
├── default-avatar.png (200x200px)
├── empty-cart.png (200x200px)
└── empty-dish.png (200x200px)
```

#### 🎨 图标建议
- **首页**：房子图标
- **菜单**：菜单/列表图标  
- **购物车**：购物车图标
- **个人中心**：用户/人像图标

### 6. 调试技巧

#### 🔍 控制台调试
```javascript
// 在控制台中测试
console.log('小程序启动测试')

// 测试全局数据
const app = getApp()
console.log('全局数据:', app.globalData)

// 测试页面数据
console.log('页面数据:', this.data)
```

#### 📊 网络调试
```javascript
// 测试网络请求
wx.request({
  url: 'http://localhost:8080/api/images/test',
  success: (res) => {
    console.log('网络请求成功:', res)
  },
  fail: (err) => {
    console.log('网络请求失败:', err)
  }
})
```

### 7. 完整启动流程

#### 🎯 标准启动步骤
1. **准备工作**
   ```
   ✅ 安装微信开发者工具
   ✅ 准备图标文件（可以使用占位图）
   ✅ 启动Java后端服务
   ```

2. **导入项目**
   ```
   ✅ 打开微信开发者工具
   ✅ 选择"导入项目"
   ✅ 选择 ordering-miniprogram 目录
   ✅ 填写项目信息
   ```

3. **配置设置**
   ```
   ✅ 开启调试模式
   ✅ 不校验合法域名
   ✅ 选择合适的基础库版本
   ```

4. **编译运行**
   ```
   ✅ 点击"编译"按钮
   ✅ 查看控制台输出
   ✅ 测试基本功能
   ```

### 8. 联系支持

如果按照以上步骤仍然无法解决问题：

1. **检查错误信息**：复制控制台中的完整错误信息
2. **环境信息**：提供微信开发者工具版本、操作系统等信息
3. **问题描述**：详细描述问题现象和操作步骤

---

🎉 **按照以上步骤，小程序应该能够正常启动和运行！**
