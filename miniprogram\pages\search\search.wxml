<!--pages/search/search.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input">
      <text class="search-icon">🔍</text>
      <input 
        class="input"
        placeholder="搜索菜品" 
        value="{{keyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearchConfirm"
        focus="{{true}}"
        confirm-type="search"
      />
      <view class="clear-btn" wx:if="{{keyword}}" bindtap="onClearSearch">
        <text class="clear-icon">✕</text>
      </view>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view class="search-results" wx:if="{{searchResults.length > 0}}">
    <view class="results-header">
      <text class="results-count">找到 {{total}} 个相关菜品</text>
    </view>
    
    <view class="results-list">
      <view 
        class="result-item" 
        wx:for="{{searchResults}}" 
        wx:key="id"
        bindtap="onDishTap"
        data-item="{{item}}"
      >
        <image 
          class="result-image" 
          src="{{item.image || '/images/dish-default.jpg'}}" 
          mode="aspectFill"
        />
        <view class="result-info">
          <view class="result-header">
            <text class="result-name">{{item.name}}</text>
            <view class="result-tags">
              <text class="tag hot" wx:if="{{item.is_hot}}">热</text>
              <text class="tag new" wx:if="{{item.is_new}}">新</text>
            </view>
          </view>
          <text class="result-desc">{{item.description}}</text>
          <view class="result-meta">
            <text class="category-name">{{item.category_name}}</text>
            <text class="sales-count">已售{{item.sales_count}}</text>
          </view>
          <view class="result-footer">
            <text class="result-price">¥{{item.price}}</text>
            <view 
              class="add-btn" 
              bindtap="onAddToCart"
              data-item="{{item}}"
            >
              <text class="add-icon">+</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 无搜索结果时显示 -->
  <view class="no-results" wx:if="{{keyword && searchResults.length === 0 && !loading}}">
    <view class="no-results-content">
      <image class="no-results-image" src="/images/no-results.png" mode="aspectFit"></image>
      <text class="no-results-text">没有找到相关菜品</text>
      <text class="no-results-tip">试试其他关键词吧</text>
    </view>
  </view>

  <!-- 搜索建议（无关键词时显示） -->
  <view class="search-suggestions" wx:if="{{!keyword}}">
    <!-- 搜索历史 -->
    <view class="suggestion-section" wx:if="{{searchHistory.length > 0}}">
      <view class="section-header">
        <text class="section-title">🕒 搜索历史</text>
        <text class="clear-history" bindtap="onClearHistory">清除</text>
      </view>
      <view class="history-list">
        <view 
          class="history-item"
          wx:for="{{searchHistory}}"
          wx:key="*this"
          bindtap="onHistoryTap"
          data-keyword="{{item}}"
        >
          <text class="history-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 热门搜索 -->
    <view class="suggestion-section">
      <view class="section-header">
        <text class="section-title">🔥 热门搜索</text>
      </view>
      <view class="hot-keywords">
        <view 
          class="hot-keyword"
          wx:for="{{hotKeywords}}"
          wx:key="*this"
          bindtap="onHotKeywordTap"
          data-keyword="{{item}}"
        >
          <text class="hot-keyword-text">{{item}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>搜索中...</text>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{!loading && searchResults.length > 0 && hasMore}}">
    <text>上拉加载更多</text>
  </view>

  <!-- 没有更多 -->
  <view class="no-more" wx:if="{{!loading && searchResults.length > 0 && !hasMore}}">
    <text>没有更多了</text>
  </view>
</view>
