# 🔗 小程序与Java后端集成示例

## 📱 小程序端使用示例

### 1. 在页面中使用图片上传

#### 菜品管理页面示例
```javascript
// pages/admin/dish-manage.js
const { chooseAndUploadImage, previewImage } = require('../../utils/imageUpload')

Page({
  data: {
    dish: {
      name: '',
      description: '',
      price: 0,
      categoryId: 1,
      images: []
    }
  },

  // 上传菜品图片
  onUploadDishImage() {
    chooseAndUploadImage({
      category: 'dish',
      businessId: this.data.dish.id,
      description: '菜品图片',
      count: 5 // 最多选择5张
    }).then((images) => {
      // 单张图片返回对象，多张返回数组
      const imageList = Array.isArray(images) ? images : [images]
      
      this.setData({
        'dish.images': [...this.data.dish.images, ...imageList]
      })
      
      console.log('上传成功:', imageList)
    }).catch((error) => {
      console.error('上传失败:', error)
    })
  },

  // 预览图片
  onPreviewImage(e) {
    const { current } = e.currentTarget.dataset
    const urls = this.data.dish.images.map(img => img.url)
    previewImage(current, urls)
  },

  // 删除图片
  onDeleteImage(e) {
    const { index } = e.currentTarget.dataset
    const images = this.data.dish.images
    images.splice(index, 1)
    
    this.setData({
      'dish.images': images
    })
  }
})
```

#### 对应的WXML模板
```xml
<!-- pages/admin/dish-manage.wxml -->
<view class="dish-form">
  <!-- 基本信息 -->
  <view class="form-section">
    <input class="form-input" placeholder="菜品名称" value="{{dish.name}}" />
    <textarea class="form-textarea" placeholder="菜品描述" value="{{dish.description}}" />
    <input class="form-input" type="digit" placeholder="价格" value="{{dish.price}}" />
  </view>

  <!-- 图片上传 -->
  <view class="form-section">
    <text class="section-title">菜品图片</text>
    
    <view class="image-grid">
      <!-- 已上传的图片 -->
      <view 
        class="image-item" 
        wx:for="{{dish.images}}" 
        wx:key="id"
      >
        <image 
          class="image-preview" 
          src="{{item.thumbnailUrl || item.url}}" 
          mode="aspectFill"
          bindtap="onPreviewImage"
          data-current="{{item.url}}"
        />
        <view 
          class="image-delete" 
          bindtap="onDeleteImage"
          data-index="{{index}}"
        >
          ✕
        </view>
      </view>
      
      <!-- 上传按钮 -->
      <view 
        class="image-upload-btn" 
        bindtap="onUploadDishImage"
        wx:if="{{dish.images.length < 5}}"
      >
        <text class="upload-icon">📷</text>
        <text class="upload-text">添加图片</text>
      </view>
    </view>
  </view>
</view>
```

### 2. 轮播图管理示例

```javascript
// pages/admin/banner-manage.js
const { chooseAndUploadImage } = require('../../utils/imageUpload')

Page({
  data: {
    banners: []
  },

  onLoad() {
    this.loadBanners()
  },

  // 加载轮播图列表
  async loadBanners() {
    try {
      const res = await wx.request({
        url: 'http://localhost:8080/api/images/category/banner',
        method: 'GET'
      })
      
      if (res.data.code === 200) {
        this.setData({
          banners: res.data.data.records
        })
      }
    } catch (error) {
      console.error('加载轮播图失败:', error)
    }
  },

  // 上传轮播图
  onUploadBanner() {
    chooseAndUploadImage({
      category: 'banner',
      description: '轮播图',
      count: 1
    }).then((image) => {
      // 刷新列表
      this.loadBanners()
      
      wx.showToast({
        title: '轮播图上传成功',
        icon: 'success'
      })
    }).catch((error) => {
      console.error('上传失败:', error)
    })
  }
})
```

### 3. 用户头像上传示例

```javascript
// pages/profile/profile.js
const { chooseAndUploadImage } = require('../../utils/imageUpload')

Page({
  data: {
    userInfo: {
      nickname: '',
      avatar: '/images/default-avatar.png'
    }
  },

  // 更换头像
  onChangeAvatar() {
    chooseAndUploadImage({
      category: 'avatar',
      businessId: this.data.userInfo.id,
      description: '用户头像',
      count: 1
    }).then((image) => {
      this.setData({
        'userInfo.avatar': image.url
      })
      
      // 更新用户信息到后端
      this.updateUserInfo()
      
      wx.showToast({
        title: '头像更新成功',
        icon: 'success'
      })
    }).catch((error) => {
      console.error('头像上传失败:', error)
    })
  },

  // 更新用户信息
  async updateUserInfo() {
    try {
      await wx.request({
        url: 'http://localhost:3000/api/users/update',
        method: 'PUT',
        data: {
          avatar: this.data.userInfo.avatar
        }
      })
    } catch (error) {
      console.error('更新用户信息失败:', error)
    }
  }
})
```

## 🔧 Java后端扩展示例

### 1. 菜品服务集成图片管理

```java
// DishService.java
@Service
public class DishService {
    
    @Autowired
    private ImageService imageService;
    
    @Autowired
    private DishMapper dishMapper;
    
    /**
     * 创建菜品并关联图片
     */
    @Transactional
    public Dish createDish(DishCreateRequest request) {
        // 创建菜品
        Dish dish = new Dish()
                .setName(request.getName())
                .setDescription(request.getDescription())
                .setPrice(request.getPrice())
                .setCategoryId(request.getCategoryId());
        
        dishMapper.insert(dish);
        
        // 关联图片
        if (request.getImageIds() != null && !request.getImageIds().isEmpty()) {
            updateDishImages(dish.getId(), request.getImageIds());
        }
        
        return dish;
    }
    
    /**
     * 更新菜品图片关联
     */
    public void updateDishImages(Long dishId, List<Long> imageIds) {
        // 更新图片的business_id
        for (Long imageId : imageIds) {
            Image image = new Image()
                    .setId(imageId)
                    .setBusinessId(dishId)
                    .setCategory("dish");
            imageService.updateById(image);
        }
        
        // 更新菜品主图
        if (!imageIds.isEmpty()) {
            Image mainImage = imageService.getById(imageIds.get(0));
            if (mainImage != null) {
                Dish dish = new Dish()
                        .setId(dishId)
                        .setImage(mainImage.getUrl());
                dishMapper.updateById(dish);
            }
        }
    }
    
    /**
     * 获取菜品详情（包含图片）
     */
    public DishDetailVO getDishDetail(Long dishId) {
        Dish dish = dishMapper.selectById(dishId);
        if (dish == null) {
            return null;
        }
        
        // 获取关联图片
        List<Image> images = imageService.getImagesByCategoryAndBusinessId("dish", dishId);
        
        return new DishDetailVO()
                .setId(dish.getId())
                .setName(dish.getName())
                .setDescription(dish.getDescription())
                .setPrice(dish.getPrice())
                .setMainImage(dish.getImage())
                .setImages(images);
    }
}
```

### 2. 图片清理定时任务

```java
// ImageCleanupTask.java
@Component
public class ImageCleanupTask {
    
    @Autowired
    private ImageService imageService;
    
    @Autowired
    private FileUploadConfig fileUploadConfig;
    
    /**
     * 每天凌晨2点清理无效图片
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupInvalidImages() {
        log.info("开始清理无效图片...");
        
        // 获取所有图片记录
        List<Image> allImages = imageService.list();
        int cleanupCount = 0;
        
        for (Image image : allImages) {
            String filePath = fileUploadConfig.getPath() + image.getFilePath();
            File file = new File(filePath);
            
            // 如果文件不存在，删除数据库记录
            if (!file.exists()) {
                imageService.removeById(image.getId());
                cleanupCount++;
                log.info("清理无效图片记录: {}", image.getFilePath());
            }
        }
        
        log.info("图片清理完成，共清理 {} 条无效记录", cleanupCount);
    }
    
    /**
     * 每周日凌晨3点清理临时文件
     */
    @Scheduled(cron = "0 0 3 ? * SUN")
    public void cleanupTempFiles() {
        log.info("开始清理临时文件...");
        
        String tempDir = fileUploadConfig.getPath() + "temp/";
        File tempFolder = new File(tempDir);
        
        if (tempFolder.exists() && tempFolder.isDirectory()) {
            File[] files = tempFolder.listFiles();
            if (files != null) {
                for (File file : files) {
                    // 删除7天前的临时文件
                    long daysDiff = (System.currentTimeMillis() - file.lastModified()) / (24 * 60 * 60 * 1000);
                    if (daysDiff > 7) {
                        file.delete();
                        log.info("清理临时文件: {}", file.getName());
                    }
                }
            }
        }
        
        log.info("临时文件清理完成");
    }
}
```

### 3. 图片统计接口

```java
// ImageStatisticsController.java
@RestController
@RequestMapping("/images/statistics")
public class ImageStatisticsController {
    
    @Autowired
    private ImageMapper imageMapper;
    
    /**
     * 获取图片统计信息
     */
    @GetMapping("/summary")
    public Result<Map<String, Object>> getImageStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 按分类统计
        Map<String, Integer> categoryStats = new HashMap<>();
        categoryStats.put("dish", imageMapper.countByCategory("dish"));
        categoryStats.put("banner", imageMapper.countByCategory("banner"));
        categoryStats.put("avatar", imageMapper.countByCategory("avatar"));
        categoryStats.put("other", imageMapper.countByCategory("other"));
        
        // 总计
        int totalCount = categoryStats.values().stream().mapToInt(Integer::intValue).sum();
        
        // 最近上传
        List<Image> recentImages = imageMapper.getRecentImages(10);
        
        statistics.put("categoryStats", categoryStats);
        statistics.put("totalCount", totalCount);
        statistics.put("recentImages", recentImages);
        
        return Result.success(statistics);
    }
}
```

## 🔄 完整的业务流程示例

### 1. 菜品发布流程

```javascript
// 小程序端 - 发布菜品
async function publishDish() {
  try {
    // 1. 上传菜品图片
    const images = await chooseAndUploadImage({
      category: 'dish',
      description: '菜品图片',
      count: 3
    })
    
    // 2. 创建菜品
    const dishData = {
      name: '宫保鸡丁',
      description: '经典川菜',
      price: 32.00,
      categoryId: 1,
      imageIds: images.map(img => img.id)
    }
    
    const res = await wx.request({
      url: 'http://localhost:8080/api/dishes',
      method: 'POST',
      data: dishData
    })
    
    if (res.data.code === 200) {
      wx.showToast({
        title: '菜品发布成功',
        icon: 'success'
      })
    }
    
  } catch (error) {
    console.error('发布失败:', error)
    wx.showToast({
      title: '发布失败',
      icon: 'none'
    })
  }
}
```

### 2. 图片管理完整示例

```javascript
// 图片管理页面
Page({
  data: {
    images: [],
    selectedCategory: 'dish',
    loading: false
  },

  onLoad() {
    this.loadImages()
  },

  // 加载图片列表
  async loadImages() {
    this.setData({ loading: true })
    
    try {
      const res = await wx.request({
        url: `http://localhost:8080/api/images/category/${this.data.selectedCategory}`,
        method: 'GET'
      })
      
      if (res.data.code === 200) {
        this.setData({
          images: res.data.data.records
        })
      }
    } catch (error) {
      console.error('加载图片失败:', error)
    } finally {
      this.setData({ loading: false })
    }
  },

  // 切换分类
  onCategoryChange(e) {
    this.setData({
      selectedCategory: e.detail.value
    })
    this.loadImages()
  },

  // 删除图片
  async onDeleteImage(e) {
    const { id } = e.currentTarget.dataset
    
    try {
      const res = await wx.showModal({
        title: '确认删除',
        content: '确定要删除这张图片吗？'
      })
      
      if (res.confirm) {
        await wx.request({
          url: `http://localhost:8080/api/images/${id}`,
          method: 'DELETE'
        })
        
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
        
        this.loadImages()
      }
    } catch (error) {
      console.error('删除失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      })
    }
  }
})
```

## 📝 注意事项

### 1. 网络配置
- 开发时需要在微信开发者工具中开启"不校验合法域名"
- 生产环境需要在微信公众平台配置合法域名

### 2. 图片优化
- 上传前可以使用小程序的压缩功能
- Java后端会自动生成缩略图
- 建议使用缩略图进行列表展示

### 3. 错误处理
- 网络请求失败时提供友好提示
- 图片上传失败时允许重试
- 大文件上传时显示进度

### 4. 性能优化
- 图片列表使用分页加载
- 使用图片懒加载
- 缓存已上传的图片信息

---

✅ **完整的小程序与Java后端集成方案已完成！**
