package com.ordering.common;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 统一返回结果类
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Data
@Accessors(chain = true)
public class Result<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 私有构造方法
     */
    private Result() {
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * 成功响应（无数据）
     * 
     * @param <T> 数据类型
     * @return 结果对象
     */
    public static <T> Result<T> success() {
        return new Result<T>()
                .setCode(200)
                .setMessage("操作成功");
    }

    /**
     * 成功响应（有数据）
     * 
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 结果对象
     */
    public static <T> Result<T> success(T data) {
        return new Result<T>()
                .setCode(200)
                .setMessage("操作成功")
                .setData(data);
    }

    /**
     * 成功响应（有数据和消息）
     * 
     * @param data 响应数据
     * @param message 响应消息
     * @param <T> 数据类型
     * @return 结果对象
     */
    public static <T> Result<T> success(T data, String message) {
        return new Result<T>()
                .setCode(200)
                .setMessage(message)
                .setData(data);
    }

    /**
     * 失败响应
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 结果对象
     */
    public static <T> Result<T> error(String message) {
        return new Result<T>()
                .setCode(500)
                .setMessage(message);
    }

    /**
     * 失败响应（自定义错误码）
     * 
     * @param code 错误码
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 结果对象
     */
    public static <T> Result<T> error(Integer code, String message) {
        return new Result<T>()
                .setCode(code)
                .setMessage(message);
    }

    /**
     * 参数错误响应
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 结果对象
     */
    public static <T> Result<T> badRequest(String message) {
        return new Result<T>()
                .setCode(400)
                .setMessage(message);
    }

    /**
     * 未授权响应
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 结果对象
     */
    public static <T> Result<T> unauthorized(String message) {
        return new Result<T>()
                .setCode(401)
                .setMessage(message);
    }

    /**
     * 禁止访问响应
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 结果对象
     */
    public static <T> Result<T> forbidden(String message) {
        return new Result<T>()
                .setCode(403)
                .setMessage(message);
    }

    /**
     * 资源不存在响应
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 结果对象
     */
    public static <T> Result<T> notFound(String message) {
        return new Result<T>()
                .setCode(404)
                .setMessage(message);
    }

    /**
     * 判断是否成功
     * 
     * @return 是否成功
     */
    public boolean isSuccess() {
        return this.code != null && this.code == 200;
    }

    /**
     * 判断是否失败
     * 
     * @return 是否失败
     */
    public boolean isError() {
        return !isSuccess();
    }
}
