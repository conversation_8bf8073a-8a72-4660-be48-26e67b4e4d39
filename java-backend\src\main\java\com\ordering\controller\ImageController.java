package com.ordering.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ordering.common.Result;
import com.ordering.entity.Image;
import com.ordering.service.ImageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 图片管理控制器
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/images")
@CrossOrigin(origins = "*")
public class ImageController {

    @Autowired
    private ImageService imageService;

    /**
     * 测试接口
     */
    @GetMapping("/test")
    public Result<String> test() {
        log.info("测试接口被调用");
        return Result.success("API服务正常运行！");
    }

    /**
     * 上传单个图片
     * 
     * @param file 图片文件
     * @param category 图片分类（dish-菜品图片, banner-轮播图, avatar-头像, other-其他）
     * @param businessId 业务ID（可选）
     * @param description 图片描述（可选）
     * @return 上传结果
     */
    @PostMapping("/upload")
    public Result<Image> uploadImage(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "category", defaultValue = "other") String category,
            @RequestParam(value = "businessId", required = false) Long businessId,
            @RequestParam(value = "description", required = false) String description) {
        
        try {
            log.info("接收图片上传请求: 文件名={}, 分类={}, 业务ID={}", 
                    file.getOriginalFilename(), category, businessId);
            
            Image image = imageService.uploadImage(file, category, businessId, description);
            return Result.success(image, "图片上传成功");
            
        } catch (Exception e) {
            log.error("图片上传失败: {}", e.getMessage(), e);
            return Result.error("图片上传失败: " + e.getMessage());
        }
    }

    /**
     * 批量上传图片
     * 
     * @param files 图片文件数组
     * @param category 图片分类
     * @param businessId 业务ID（可选）
     * @param description 图片描述（可选）
     * @return 上传结果
     */
    @PostMapping("/upload/batch")
    public Result<List<Image>> uploadImages(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam(value = "category", defaultValue = "other") String category,
            @RequestParam(value = "businessId", required = false) Long businessId,
            @RequestParam(value = "description", required = false) String description) {
        
        try {
            log.info("接收批量图片上传请求: 文件数量={}, 分类={}, 业务ID={}", 
                    files.length, category, businessId);
            
            List<Image> images = imageService.uploadImages(files, category, businessId, description);
            return Result.success(images, "批量上传成功");
            
        } catch (Exception e) {
            log.error("批量图片上传失败: {}", e.getMessage(), e);
            return Result.error("批量上传失败: " + e.getMessage());
        }
    }

    /**
     * 根据分类获取图片列表（分页）
     * 
     * @param category 图片分类
     * @param page 页码（默认1）
     * @param size 每页大小（默认10）
     * @return 图片列表
     */
    @GetMapping("/category/{category}")
    public Result<IPage<Image>> getImagesByCategory(
            @PathVariable String category,
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "size", defaultValue = "10") int size) {
        
        try {
            IPage<Image> images = imageService.getImagesByCategory(category, page, size);
            return Result.success(images);
            
        } catch (Exception e) {
            log.error("获取分类图片失败: {}", e.getMessage(), e);
            return Result.error("获取图片失败: " + e.getMessage());
        }
    }

    /**
     * 根据业务ID获取图片列表
     * 
     * @param businessId 业务ID
     * @return 图片列表
     */
    @GetMapping("/business/{businessId}")
    public Result<List<Image>> getImagesByBusinessId(@PathVariable Long businessId) {
        try {
            List<Image> images = imageService.getImagesByBusinessId(businessId);
            return Result.success(images);
            
        } catch (Exception e) {
            log.error("获取业务图片失败: {}", e.getMessage(), e);
            return Result.error("获取图片失败: " + e.getMessage());
        }
    }

    /**
     * 根据分类和业务ID获取图片列表
     * 
     * @param category 图片分类
     * @param businessId 业务ID
     * @return 图片列表
     */
    @GetMapping("/category/{category}/business/{businessId}")
    public Result<List<Image>> getImagesByCategoryAndBusinessId(
            @PathVariable String category,
            @PathVariable Long businessId) {
        
        try {
            List<Image> images = imageService.getImagesByCategoryAndBusinessId(category, businessId);
            return Result.success(images);
            
        } catch (Exception e) {
            log.error("获取分类业务图片失败: {}", e.getMessage(), e);
            return Result.error("获取图片失败: " + e.getMessage());
        }
    }

    /**
     * 获取图片详情
     * 
     * @param id 图片ID
     * @return 图片详情
     */
    @GetMapping("/{id}")
    public Result<Image> getImageDetail(@PathVariable Long id) {
        try {
            Image image = imageService.getImageDetail(id);
            if (image == null) {
                return Result.error("图片不存在");
            }
            return Result.success(image);
            
        } catch (Exception e) {
            log.error("获取图片详情失败: {}", e.getMessage(), e);
            return Result.error("获取图片详情失败: " + e.getMessage());
        }
    }

    /**
     * 更新图片信息
     * 
     * @param id 图片ID
     * @param requestBody 请求体
     * @return 更新结果
     */
    @PutMapping("/{id}")
    public Result<String> updateImageInfo(
            @PathVariable Long id,
            @RequestBody Map<String, Object> requestBody) {
        
        try {
            String name = (String) requestBody.get("name");
            String description = (String) requestBody.get("description");
            Integer sortOrder = (Integer) requestBody.get("sortOrder");
            
            boolean success = imageService.updateImageInfo(id, name, description, sortOrder);
            if (success) {
                return Result.success("图片信息更新成功");
            } else {
                return Result.error("图片信息更新失败");
            }
            
        } catch (Exception e) {
            log.error("更新图片信息失败: {}", e.getMessage(), e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除图片
     * 
     * @param id 图片ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteImage(@PathVariable Long id) {
        try {
            boolean success = imageService.deleteImage(id);
            if (success) {
                return Result.success("图片删除成功");
            } else {
                return Result.error("图片删除失败");
            }
            
        } catch (Exception e) {
            log.error("删除图片失败: {}", e.getMessage(), e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除图片
     * 
     * @param requestBody 请求体，包含图片ID列表
     * @return 删除结果
     */
    @DeleteMapping("/batch")
    public Result<String> deleteImages(@RequestBody Map<String, List<Long>> requestBody) {
        try {
            List<Long> ids = requestBody.get("ids");
            if (ids == null || ids.isEmpty()) {
                return Result.error("请选择要删除的图片");
            }
            
            int count = imageService.deleteImages(ids);
            return Result.success("成功删除 " + count + " 张图片");
            
        } catch (Exception e) {
            log.error("批量删除图片失败: {}", e.getMessage(), e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有图片列表（分页）
     * 
     * @param page 页码
     * @param size 每页大小
     * @return 图片列表
     */
    @GetMapping("/list")
    public Result<IPage<Image>> getAllImages(
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "size", defaultValue = "10") int size) {
        
        try {
            IPage<Image> images = imageService.page(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(page, size));
            return Result.success(images);
            
        } catch (Exception e) {
            log.error("获取图片列表失败: {}", e.getMessage(), e);
            return Result.error("获取图片列表失败: " + e.getMessage());
        }
    }
}
