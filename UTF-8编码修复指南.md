# 🔧 UTF-8编码修复指南

## 问题描述
微信开发者工具报错：
```
[ appservice 生成错误] project.config.json: project.config.json 文件不是 UTF-8 格式
(env: Windows,mp,1.06.2412050; lib: 3.8.5)
```

## 🛠️ 解决方案

### 方案一：手动创建文件（推荐）

1. **删除现有文件**
   - 在 `miniprogram` 目录下删除 `project.config.json` 文件

2. **使用文本编辑器创建新文件**
   - 使用 VS Code、Notepad++ 或其他支持UTF-8的编辑器
   - 创建新文件 `project.config.json`
   - 设置编码为 UTF-8（无BOM）

3. **复制以下内容**
```json
{
  "description": "WeChat Mini Program",
  "setting": {
    "urlCheck": false,
    "es6": true,
    "enhance": true,
    "postcss": true,
    "minified": true,
    "newFeature": false,
    "coverView": true,
    "nodeModules": false,
    "autoAudits": false,
    "showShadowRootInWxmlPanel": true,
    "scopeDataCheck": false,
    "checkInvalidKey": true,
    "checkSiteMap": true,
    "uploadWithSourceMap": true,
    "compileHotReLoad": false,
    "useMultiFrameRuntime": true,
    "useApiHook": true,
    "useApiHostProcess": true,
    "minifyWXSS": true,
    "minifyWXML": true
  },
  "compileType": "miniprogram",
  "libVersion": "3.8.5",
  "appid": "wx1234567890abcdef",
  "projectname": "ordering-system"
}
```

### 方案二：使用VS Code

1. **打开VS Code**
2. **打开 miniprogram 文件夹**
3. **创建新文件** `project.config.json`
4. **检查编码**
   - 右下角显示编码格式
   - 确保是 "UTF-8"
5. **粘贴配置内容**
6. **保存文件** (Ctrl+S)

### 方案三：使用Notepad++

1. **打开Notepad++**
2. **新建文件**
3. **设置编码**
   - 菜单：编码 → 转为UTF-8编码（无BOM）
4. **粘贴配置内容**
5. **保存为** `project.config.json`

### 方案四：使用PowerShell（高级）

```powershell
# 进入项目目录
cd "你的项目路径\miniprogram"

# 创建UTF-8编码的文件
$content = @'
{
  "description": "WeChat Mini Program",
  "setting": {
    "urlCheck": false,
    "es6": true,
    "enhance": true,
    "postcss": true,
    "minified": true,
    "newFeature": false,
    "coverView": true,
    "nodeModules": false,
    "autoAudits": false,
    "showShadowRootInWxmlPanel": true,
    "scopeDataCheck": false,
    "checkInvalidKey": true,
    "checkSiteMap": true,
    "uploadWithSourceMap": true,
    "compileHotReLoad": false,
    "useMultiFrameRuntime": true,
    "useApiHook": true,
    "useApiHostProcess": true,
    "minifyWXSS": true,
    "minifyWXML": true
  },
  "compileType": "miniprogram",
  "libVersion": "3.8.5",
  "appid": "wx1234567890abcdef",
  "projectname": "ordering-system"
}
'@

[System.IO.File]::WriteAllText("project.config.json", $content, [System.Text.Encoding]::UTF8)
```

## ✅ 验证步骤

### 1. 检查文件编码
- 使用文本编辑器打开文件
- 确认编码显示为 UTF-8
- 确保没有BOM（字节顺序标记）

### 2. 验证JSON格式
- 使用在线JSON验证器检查语法
- 确保所有引号、括号、逗号正确

### 3. 测试微信开发者工具
- 重新打开微信开发者工具
- 导入项目
- 检查是否还有编码错误

## 🔍 常见问题

### Q: 为什么会出现编码问题？
A: 通常是因为：
- 使用了不支持UTF-8的编辑器
- 文件包含了BOM标记
- 系统默认编码不是UTF-8

### Q: 如何避免编码问题？
A: 建议：
- 使用现代文本编辑器（VS Code、Sublime Text等）
- 设置编辑器默认编码为UTF-8
- 避免使用记事本等简单编辑器

### Q: 其他文件也需要UTF-8编码吗？
A: 是的，建议所有项目文件都使用UTF-8编码：
- `.js` 文件
- `.json` 文件
- `.wxml` 文件
- `.wxss` 文件

## 📋 配置说明

### 核心配置项
- `urlCheck: false` - 开发时不检查域名
- `es6: true` - 启用ES6语法支持
- `enhance: true` - 启用增强编译
- `minified: true` - 启用代码压缩

### 可选配置项
- `appid` - 小程序AppID（可使用测试号）
- `projectname` - 项目名称
- `libVersion` - 基础库版本

## 🚀 完成后的操作

1. **重启微信开发者工具**
2. **重新导入项目**
3. **检查编译状态**
4. **测试基本功能**

## 💡 预防措施

### 编辑器设置
- **VS Code**: 设置 `"files.encoding": "utf8"`
- **Sublime Text**: 设置 `"default_encoding": "UTF-8"`
- **Atom**: 默认使用UTF-8

### 团队协作
- 统一使用UTF-8编码
- 在项目README中说明编码要求
- 使用.editorconfig文件统一编码设置

---

✅ **按照以上步骤操作后，UTF-8编码问题应该得到解决！**
