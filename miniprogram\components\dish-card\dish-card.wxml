<!--components/dish-card/dish-card.wxml-->
<view class="dish-card dish-card-{{mode}}" bindtap="onDishTap">
  <!-- 图片容器 -->
  <view class="image-container">
    <image 
      class="dish-image" 
      src="{{getImageUrl(dish.image)}}" 
      mode="aspectFill"
      lazy-load="{{true}}"
    />
    
    <!-- 标签 -->
    <view class="tags" wx:if="{{dish.is_hot || dish.is_new}}">
      <text class="tag hot" wx:if="{{dish.is_hot}}">🔥 热门</text>
      <text class="tag new" wx:if="{{dish.is_new}}">✨ 新品</text>
    </view>

    <!-- 点赞按钮 -->
    <view 
      class="like-btn {{isLiked ? 'liked' : ''}}" 
      bindtap="onToggleLike"
      animation="{{likeAnimation}}"
    >
      <text class="like-icon">{{isLiked ? '❤️' : '🤍'}}</text>
    </view>

    <!-- 图片遮罩 -->
    <view class="image-overlay"></view>
  </view>

  <!-- 内容区域 -->
  <view class="content">
    <view class="dish-info">
      <text class="dish-name">{{dish.name}}</text>
      <text class="dish-desc" wx:if="{{mode !== 'compact'}}">{{dish.description}}</text>
      
      <!-- 销量和评分 -->
      <view class="meta-info" wx:if="{{mode !== 'compact'}}">
        <view class="sales">
          <text class="sales-icon">📊</text>
          <text class="sales-text">已售{{dish.sales_count || 0}}</text>
        </view>
        <view class="rating">
          <text class="rating-stars">⭐⭐⭐⭐⭐</text>
          <text class="rating-text">4.8</text>
        </view>
      </view>
    </view>

    <!-- 价格和操作 -->
    <view class="footer">
      <view class="price-section">
        <text class="price">¥{{formatPrice(dish.price)}}</text>
        <text class="price-unit" wx:if="{{mode === 'featured'}}">/份</text>
      </view>
      
      <view 
        class="add-btn" 
        bindtap="onAddToCart"
        animation="{{addAnimation}}"
      >
        <text class="add-icon">+</text>
      </view>
    </view>
  </view>

  <!-- 特色模式的额外装饰 -->
  <view class="featured-decoration" wx:if="{{mode === 'featured'}}">
    <view class="sparkle sparkle-1">✨</view>
    <view class="sparkle sparkle-2">⭐</view>
    <view class="sparkle sparkle-3">💫</view>
  </view>

  <!-- 悬浮光效 -->
  <view class="hover-glow"></view>
</view>
