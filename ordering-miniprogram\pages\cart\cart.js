// pages/cart/cart.js
const app = getApp()

Page({
  data: {
    // 购物车商品
    cartItems: [],
    
    // 费用相关
    subtotal: 0,
    deliveryFee: 5,
    couponDiscount: 0,
    totalAmount: 0,
    totalQuantity: 0,
    minOrderAmount: 20,
    
    // 配送信息
    selectedAddress: null,
    deliveryTime: 30,
    
    // 优惠券
    selectedCoupon: null,
    availableCoupons: [],
    
    // 备注
    remark: '',
    
    // 弹窗状态
    showClearModal: false
  },

  onLoad() {
    console.log('购物车页面加载')
  },

  onShow() {
    console.log('购物车页面显示')
    this.loadCartData()
    this.loadUserData()
  },

  // 加载购物车数据
  loadCartData() {
    const cartItems = app.globalData.cart || []
    const subtotal = app.globalData.cartTotal || 0
    const totalQuantity = app.getCartItemCount()
    
    // 计算总金额
    const totalAmount = subtotal + this.data.deliveryFee - this.data.couponDiscount
    
    this.setData({
      cartItems,
      subtotal: subtotal.toFixed(2),
      totalAmount: Math.max(0, totalAmount).toFixed(2),
      totalQuantity
    })
  },

  // 加载用户数据
  loadUserData() {
    // 加载用户地址
    this.loadSelectedAddress()
    
    // 加载可用优惠券
    this.loadAvailableCoupons()
  },

  // 加载选中的地址
  loadSelectedAddress() {
    try {
      const selectedAddress = wx.getStorageSync('selectedAddress')
      if (selectedAddress) {
        this.setData({ selectedAddress })
      }
    } catch (error) {
      console.error('加载地址失败:', error)
    }
  },

  // 加载可用优惠券
  loadAvailableCoupons() {
    // 模拟优惠券数据
    const availableCoupons = [
      {
        id: 1,
        name: '满30减5',
        type: 'amount',
        condition: 30,
        discount: 5,
        description: '满30元可用'
      },
      {
        id: 2,
        name: '满50减10',
        type: 'amount',
        condition: 50,
        discount: 10,
        description: '满50元可用'
      },
      {
        id: 3,
        name: '新用户8折',
        type: 'percent',
        condition: 0,
        discount: 0.8,
        description: '新用户专享'
      }
    ]
    
    // 筛选可用的优惠券
    const validCoupons = availableCoupons.filter(coupon => {
      if (coupon.type === 'amount') {
        return this.data.subtotal >= coupon.condition
      }
      return true
    })
    
    this.setData({ availableCoupons: validCoupons })
  },

  // 计算总金额
  calculateTotal() {
    const subtotal = parseFloat(this.data.subtotal)
    const deliveryFee = this.data.deliveryFee
    const couponDiscount = this.data.couponDiscount
    
    const totalAmount = Math.max(0, subtotal + deliveryFee - couponDiscount)
    
    this.setData({
      totalAmount: totalAmount.toFixed(2)
    })
  },

  // 增加商品数量
  onIncreaseQuantity(e) {
    const item = e.currentTarget.dataset.item
    console.log('增加数量:', item)
    
    app.updateCartQuantity(item.id, item.quantity + 1)
    this.loadCartData()
  },

  // 减少商品数量
  onDecreaseQuantity(e) {
    const item = e.currentTarget.dataset.item
    console.log('减少数量:', item)
    
    if (item.quantity > 1) {
      app.updateCartQuantity(item.id, item.quantity - 1)
    } else {
      app.removeFromCart(item.id)
    }
    
    this.loadCartData()
  },

  // 选择优惠券
  onSelectCoupon() {
    if (this.data.availableCoupons.length === 0) {
      wx.showToast({
        title: '暂无可用优惠券',
        icon: 'none'
      })
      return
    }

    const couponList = this.data.availableCoupons.map(coupon => coupon.name)
    
    wx.showActionSheet({
      itemList: couponList,
      success: (res) => {
        const selectedCoupon = this.data.availableCoupons[res.tapIndex]
        console.log('选择优惠券:', selectedCoupon)
        
        // 计算优惠金额
        let discount = 0
        if (selectedCoupon.type === 'amount') {
          discount = selectedCoupon.discount
        } else if (selectedCoupon.type === 'percent') {
          discount = parseFloat(this.data.subtotal) * (1 - selectedCoupon.discount)
        }
        
        this.setData({
          selectedCoupon,
          couponDiscount: discount.toFixed(2)
        })
        
        this.calculateTotal()
        
        wx.showToast({
          title: '优惠券已选择',
          icon: 'success'
        })
      }
    })
  },

  // 选择地址
  onSelectAddress() {
    wx.navigateTo({
      url: '/pages/address/address?from=cart'
    })
  },

  // 备注输入
  onRemarkInput(e) {
    this.setData({
      remark: e.detail.value
    })
  },

  // 去逛逛
  onGoShopping() {
    wx.switchTab({
      url: '/pages/menu/menu'
    })
  },

  // 去结算
  onCheckout() {
    const totalAmount = parseFloat(this.data.totalAmount)
    const minOrderAmount = this.data.minOrderAmount
    
    if (totalAmount < minOrderAmount) {
      wx.showToast({
        title: `还差¥${(minOrderAmount - totalAmount).toFixed(2)}起送`,
        icon: 'none'
      })
      return
    }
    
    if (!this.data.selectedAddress) {
      wx.showToast({
        title: '请选择配送地址',
        icon: 'none'
      })
      return
    }
    
    // 准备订单数据
    const orderData = {
      items: this.data.cartItems,
      address: this.data.selectedAddress,
      subtotal: this.data.subtotal,
      deliveryFee: this.data.deliveryFee,
      couponDiscount: this.data.couponDiscount,
      totalAmount: this.data.totalAmount,
      remark: this.data.remark,
      coupon: this.data.selectedCoupon
    }
    
    // 保存订单数据到全局
    app.globalData.orderData = orderData
    
    // 跳转到订单确认页面
    wx.navigateTo({
      url: '/pages/order/order'
    })
  },

  // 显示清空确认弹窗
  onShowClearModal() {
    this.setData({ showClearModal: true })
  },

  // 确认清空购物车
  onConfirmClear() {
    app.clearCart()
    this.loadCartData()
    this.setData({ showClearModal: false })
    
    wx.showToast({
      title: '购物车已清空',
      icon: 'success'
    })
  },

  // 取消清空
  onCancelClear() {
    this.setData({ showClearModal: false })
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '美食点餐 - 购物车',
      path: '/pages/cart/cart',
      imageUrl: '/images/share-cart.png'
    }
  }
})
