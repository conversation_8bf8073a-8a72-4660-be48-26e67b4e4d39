/* pages/index/index.wxss */

.container {
  padding: 0;
  background: #f8f8f8;
}

/* 搜索栏 */
.search-bar {
  padding: 20rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-input {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: #f8f8f8;
  border-radius: 50rpx;
}

.search-icon {
  margin-right: 20rpx;
}

.search-placeholder {
  color: #999;
  font-size: 28rpx;
}

/* 轮播图 */
.banner-section {
  margin-bottom: 20rpx;
}

.banner-swiper {
  height: 320rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 分类导航 */
.category-section {
  background: #fff;
  margin-bottom: 20rpx;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.more-text {
  font-size: 24rpx;
  color: #999;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  padding: 20rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40rpx;
  padding: 20rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.category-item.active {
  background: rgba(255, 107, 53, 0.1);
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 12rpx;
}

.category-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

.category-item.active .category-name {
  color: #ff6b35;
  font-weight: 500;
}

/* 推荐菜品 */
.recommend-section {
  background: #fff;
  margin-bottom: 20rpx;
}

.dish-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 0 20rpx 20rpx;
}

.dish-card {
  position: relative;
  width: calc(50% - 10rpx);
  margin: 0 5rpx 20rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.dish-image {
  width: 100%;
  height: 240rpx;
}

.dish-info {
  padding: 20rpx;
}

.dish-name {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dish-desc {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dish-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dish-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff6b35;
}

.price-symbol {
  font-size: 24rpx;
}

.add-btn {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
}

.dish-tags {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
}

.tag {
  display: inline-block;
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 500;
  margin-right: 8rpx;
}

.tag-hot {
  background: #ff4757;
  color: #fff;
}

.tag-new {
  background: #2ecc71;
  color: #fff;
}

/* 热门菜品 */
.hot-section {
  background: #fff;
  margin-bottom: 20rpx;
}

.hot-list {
  padding: 0 20rpx 20rpx;
}

.hot-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.hot-item:last-child {
  border-bottom: none;
}

.hot-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.hot-info {
  flex: 1;
  margin-right: 20rpx;
}

.hot-name {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.hot-desc {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.hot-meta {
  display: flex;
  align-items: center;
}

.hot-sales {
  font-size: 22rpx;
  color: #999;
  margin-right: 20rpx;
}

.hot-rating {
  font-size: 22rpx;
  color: #ffc107;
}

.hot-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff6b35;
  margin-right: 20rpx;
}

.hot-add {
  width: 50rpx;
  height: 50rpx;
  border: 2rpx solid #ff6b35;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 新品菜品 */
.new-section {
  background: #fff;
  margin-bottom: 20rpx;
}

.new-scroll {
  white-space: nowrap;
}

.new-list {
  display: flex;
  padding: 20rpx;
}

.new-item {
  position: relative;
  width: 240rpx;
  margin-right: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.new-image {
  width: 100%;
  height: 180rpx;
}

.new-info {
  padding: 20rpx;
}

.new-name {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.new-price {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff6b35;
}

.new-tag {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  background: #2ecc71;
  color: #fff;
  font-size: 18rpx;
  font-weight: 500;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
}

/* 购物车悬浮按钮 */
.cart-float {
  position: fixed;
  bottom: 120rpx;
  right: 30rpx;
  z-index: 999;
}

.cart-icon {
  position: relative;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.4);
}

.cart-count {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4757;
  color: #fff;
  font-size: 20rpx;
  font-weight: 500;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
  font-size: 28rpx;
  color: #999;
}
