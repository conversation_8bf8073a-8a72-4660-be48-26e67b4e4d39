{"entryPagePath": "pages/index/index", "pages": ["pages/index/index", "pages/category/category", "pages/detail/detail", "pages/cart/cart", "pages/order/order", "pages/order-detail/order-detail", "pages/search/search", "pages/login/login", "pages/profile/profile", "pages/debug/debug"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#ff6b35", "navigationBarTitleText": "美食天下", "navigationBarTextStyle": "white", "backgroundColor": "#f5f5f5", "enablePullDownRefresh": false}, "tabBar": {"color": "#999999", "selectedColor": "#ff6b35", "backgroundColor": "#ffffff", "borderStyle": "black", "list": [{"pagePath": "pages/index/index", "text": "🏠 首页"}, {"pagePath": "pages/category/category", "text": "📋 分类"}, {"pagePath": "pages/cart/cart", "text": "🛒 购物车"}, {"pagePath": "pages/profile/profile", "text": "👤 我的"}]}, "networkTimeout": {"request": 10000, "downloadFile": 10000}, "debug": false, "navigateToMiniProgramAppIdList": [], "permission": {"scope.userLocation": {"desc": "您的位置信息将用于小程序位置接口的效果展示"}}, "requiredBackgroundModes": [], "plugins": {}, "preloadRule": {"pages/index/index": {"network": "all", "packages": ["components"]}}, "subPackages": [], "workers": "", "functionalPages": false, "sitemapLocation": "sitemap.json", "style": "v2", "lazyCodeLoading": "requiredComponents"}