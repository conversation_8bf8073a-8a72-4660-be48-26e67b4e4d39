const express = require('express')
const { query } = require('../config/database')
const router = express.Router()

// 获取轮播图列表
router.get('/', async (req, res) => {
  try {
    const banners = await query(`
      SELECT id, title, image, link_type, link_id, link_url, sort_order
      FROM banners 
      WHERE status = 1 
      ORDER BY sort_order DESC, id DESC
    `)

    res.json({
      code: 0,
      message: '获取成功',
      data: banners
    })
  } catch (error) {
    console.error('获取轮播图错误:', error)
    res.status(500).json({
      code: 500,
      message: '获取轮播图失败'
    })
  }
})

module.exports = router
