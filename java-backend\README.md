# 🍽️ 在线点餐系统 - Java后端服务

专为微信小程序在线点餐系统设计的Java后端服务，主要负责图片管理功能。

## ✨ 功能特色

### 🖼️ 图片管理核心功能
- **图片上传**: 支持单个和批量图片上传
- **图片压缩**: 自动压缩大图片，优化存储空间
- **缩略图生成**: 自动生成缩略图，提升加载速度
- **分类管理**: 支持菜品图片、轮播图、头像等分类
- **业务关联**: 图片可关联具体业务ID（如菜品ID）

### 🔧 技术特性
- **Spring Boot 2.7**: 现代化Java开发框架
- **MyBatis Plus**: 高效的ORM框架
- **MySQL 8.0**: 可靠的关系型数据库
- **Thumbnailator**: 专业的图片处理库
- **统一异常处理**: 完善的错误处理机制

## 🏗️ 项目结构

```
java-backend/
├── src/main/java/com/ordering/
│   ├── OrderingSystemApplication.java    # 启动类
│   ├── config/                          # 配置类
│   │   ├── FileUploadConfig.java        # 文件上传配置
│   │   └── WebConfig.java               # Web配置
│   ├── controller/                      # 控制器
│   │   └── ImageController.java         # 图片管理控制器
│   ├── service/                         # 服务层
│   │   ├── ImageService.java            # 图片服务接口
│   │   └── impl/ImageServiceImpl.java   # 图片服务实现
│   ├── entity/                          # 实体类
│   │   └── Image.java                   # 图片实体
│   ├── mapper/                          # 数据访问层
│   │   └── ImageMapper.java             # 图片Mapper
│   └── common/                          # 通用类
│       ├── Result.java                  # 统一返回结果
│       └── GlobalExceptionHandler.java  # 全局异常处理
├── src/main/resources/
│   ├── application.yml                  # 应用配置
│   └── sql/init.sql                     # 数据库初始化脚本
├── uploads/                             # 图片上传目录
├── pom.xml                              # Maven配置
├── start.bat                            # 启动脚本
└── README.md                            # 项目说明
```

## 🚀 快速开始

### 环境要求
- **JDK**: 8 或更高版本
- **Maven**: 3.6 或更高版本
- **MySQL**: 8.0 或更高版本

### 1. 数据库配置
```sql
-- 创建数据库
CREATE DATABASE ordering_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入初始化脚本
mysql -u root -p ordering_system < src/main/resources/sql/init.sql
```

### 2. 修改配置
编辑 `src/main/resources/application.yml`:
```yaml
spring:
  datasource:
    url: *******************************************
    username: root
    password: your_password
```

### 3. 启动应用
```bash
# 方式一：使用启动脚本（Windows）
start.bat

# 方式二：使用Maven命令
mvn spring-boot:run

# 方式三：编译后运行
mvn clean package
java -jar target/ordering-system-backend-1.0.0.jar
```

### 4. 验证启动
访问 http://localhost:8080/api/images/list 查看是否正常启动。

## 📖 API文档

### 图片上传接口

#### 上传单个图片
```http
POST /api/images/upload
Content-Type: multipart/form-data

参数:
- file: 图片文件 (必需)
- category: 图片分类 (可选，默认: other)
  - dish: 菜品图片
  - banner: 轮播图
  - avatar: 头像
  - other: 其他
- businessId: 业务ID (可选)
- description: 图片描述 (可选)

响应:
{
  "code": 200,
  "message": "图片上传成功",
  "data": {
    "id": 1,
    "name": "dish_001",
    "url": "/uploads/dish/2024/01/15/abc123.jpg",
    "thumbnailUrl": "/uploads/dish/2024/01/15/thumb_abc123.jpg",
    "fileSize": 1024000,
    "width": 1920,
    "height": 1080
  }
}
```

#### 批量上传图片
```http
POST /api/images/upload/batch
Content-Type: multipart/form-data

参数:
- files: 图片文件数组 (必需)
- category: 图片分类 (可选)
- businessId: 业务ID (可选)
- description: 图片描述 (可选)
```

#### 获取图片列表
```http
GET /api/images/category/{category}?page=1&size=10
GET /api/images/business/{businessId}
GET /api/images/category/{category}/business/{businessId}
```

#### 删除图片
```http
DELETE /api/images/{id}
DELETE /api/images/batch
Content-Type: application/json
{
  "ids": [1, 2, 3]
}
```

#### 更新图片信息
```http
PUT /api/images/{id}
Content-Type: application/json
{
  "name": "新图片名称",
  "description": "图片描述",
  "sortOrder": 1
}
```

## 🔧 配置说明

### 文件上传配置
```yaml
file:
  upload:
    path: ./uploads/                    # 上传路径
    access-path: /uploads/**            # 访问路径前缀
    allowed-types: jpg,jpeg,png,gif,webp # 允许的图片格式
    max-size: 10485760                  # 最大文件大小 (10MB)
    compress:
      enabled: true                     # 是否启用压缩
      quality: 0.8                     # 压缩质量
      max-width: 1920                  # 最大宽度
      max-height: 1080                 # 最大高度
      thumbnail:
        width: 300                     # 缩略图宽度
        height: 300                    # 缩略图高度
```

### 跨域配置
```yaml
cors:
  allowed-origins: 
    - http://localhost:3000
    - https://servicewechat.com
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  allow-credentials: true
```

## 🖼️ 图片处理流程

### 上传流程
1. **文件验证**: 检查文件类型和大小
2. **生成文件名**: UUID + 原扩展名
3. **创建目录**: 按分类和日期创建目录结构
4. **保存原图**: 保存到指定目录
5. **图片压缩**: 根据配置压缩图片
6. **生成缩略图**: 创建300x300缩略图
7. **保存记录**: 将图片信息保存到数据库

### 目录结构
```
uploads/
├── dish/           # 菜品图片
│   └── 2024/01/15/
├── banner/         # 轮播图
│   └── 2024/01/15/
├── avatar/         # 头像
│   └── 2024/01/15/
└── other/          # 其他图片
    └── 2024/01/15/
```

## 🔗 与小程序集成

### 小程序端调用示例
```javascript
// 上传图片
wx.chooseImage({
  count: 1,
  success: (res) => {
    wx.uploadFile({
      url: 'http://localhost:8080/api/images/upload',
      filePath: res.tempFilePaths[0],
      name: 'file',
      formData: {
        category: 'dish',
        businessId: 1,
        description: '菜品图片'
      },
      success: (uploadRes) => {
        const data = JSON.parse(uploadRes.data);
        if (data.code === 200) {
          console.log('上传成功:', data.data.url);
        }
      }
    });
  }
});

// 获取图片列表
wx.request({
  url: 'http://localhost:8080/api/images/category/dish',
  method: 'GET',
  success: (res) => {
    if (res.data.code === 200) {
      console.log('图片列表:', res.data.data.records);
    }
  }
});
```

## 🛠️ 开发指南

### 添加新的图片分类
1. 在 `FileUploadConfig` 中添加新分类
2. 在数据库中添加相应的业务表
3. 创建对应的控制器和服务

### 自定义图片处理
继承 `ImageService` 并重写相关方法：
```java
@Service
public class CustomImageService extends ImageServiceImpl {
    @Override
    public boolean compressImage(String sourceFile, String targetFile, 
                                double quality, int maxWidth, int maxHeight) {
        // 自定义压缩逻辑
        return super.compressImage(sourceFile, targetFile, quality, maxWidth, maxHeight);
    }
}
```

## 📊 监控和维护

### 日志查看
```bash
# 查看应用日志
tail -f logs/ordering-system.log

# 查看错误日志
grep "ERROR" logs/ordering-system.log
```

### 数据库维护
```sql
-- 清理无效图片记录
DELETE FROM images WHERE deleted = 1 AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 统计图片使用情况
SELECT category, COUNT(*) as count, SUM(file_size) as total_size 
FROM images WHERE deleted = 0 GROUP BY category;
```

## 🔒 安全考虑

### 文件安全
- 限制上传文件类型
- 限制文件大小
- 文件名随机化
- 定期清理临时文件

### 访问控制
- 跨域请求限制
- 文件访问权限控制
- API接口鉴权

## 🚀 部署指南

### 生产环境配置
```yaml
# application-prod.yml
spring:
  datasource:
    url: *****************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}

file:
  upload:
    path: /data/uploads/
```

### Docker部署
```dockerfile
FROM openjdk:8-jre-alpine
COPY target/ordering-system-backend-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 📞 技术支持

如遇问题，请检查：
1. Java和Maven版本是否正确
2. MySQL数据库是否正常运行
3. 配置文件是否正确
4. 上传目录是否有写权限

---

✅ **Java后端服务已完成，可以与微信小程序完美配合使用！**
