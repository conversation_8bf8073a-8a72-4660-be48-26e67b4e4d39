/* pages/category/category.wxss */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 搜索栏 */
.search-section {
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
}

.search-bar {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid rgba(255, 107, 53, 0.2);
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.search-bar:active {
  border-color: #ff6b35;
  box-shadow: 0 6rpx 30rpx rgba(255, 107, 53, 0.2);
}

.search-icon {
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #ff6b35;
}

.search-placeholder {
  flex: 1;
  font-size: 28rpx;
  color: #999;
}

/* 内容区域 */
.content-section {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧分类栏 */
.category-sidebar {
  width: 200rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-right: 1rpx solid rgba(0, 0, 0, 0.05);
}

.category-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.category-item:active {
  background: rgba(255, 107, 53, 0.1);
}

.category-item.active {
  background: rgba(255, 107, 53, 0.1);
  color: #ff6b35;
}

.category-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-bottom: 15rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.category-name {
  font-size: 24rpx;
  text-align: center;
  line-height: 1.2;
  font-weight: 500;
}

.category-item.active .category-name {
  color: #ff6b35;
  font-weight: bold;
}

.category-indicator {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 3rpx 0 0 3rpx;
}

/* 右侧菜品区域 */
.dishes-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
}

/* 排序选项 */
.sort-section {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.8);
}

.sort-options {
  display: flex;
  gap: 30rpx;
}

.sort-item {
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  background: rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.sort-item.active {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
}

.sort-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.sort-item.active .sort-text {
  color: white;
}

/* 菜品列表 */
.dishes-list {
  padding: 20rpx;
}

/* 加载状态 */
.loading-section {
  padding: 40rpx;
  text-align: center;
}

.no-more {
  padding: 40rpx;
  text-align: center;
}

.no-more-text {
  font-size: 26rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.empty-tip {
  font-size: 26rpx;
  color: #999;
}
