<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <view class="user-info" wx:if="{{hasLogin}}">
      <image class="user-avatar" src="{{userInfo.avatar || '/images/default-avatar.png'}}" />
      <view class="user-details">
        <text class="user-name">{{userInfo.nickname || '美食爱好者'}}</text>
        <text class="user-phone">{{userInfo.phone || ''}}</text>
      </view>
      <view class="user-level">
        <text class="level-text">VIP</text>
      </view>
    </view>
    
    <view class="login-section" wx:else>
      <image class="login-avatar" src="/images/default-avatar.png" />
      <view class="login-info">
        <text class="login-title">登录享受更多服务</text>
        <text class="login-desc">查看订单、收藏菜品、专享优惠</text>
      </view>
      <button class="login-btn" bindtap="onLogin">立即登录</button>
    </view>
  </view>

  <!-- 订单统计 -->
  <view class="order-stats" wx:if="{{hasLogin}}">
    <view class="stats-item" bindtap="onOrderTap" data-status="all">
      <text class="stats-number">{{orderStats.total || 0}}</text>
      <text class="stats-label">全部订单</text>
    </view>
    <view class="stats-item" bindtap="onOrderTap" data-status="pending">
      <text class="stats-number">{{orderStats.pending || 0}}</text>
      <text class="stats-label">待付款</text>
    </view>
    <view class="stats-item" bindtap="onOrderTap" data-status="preparing">
      <text class="stats-number">{{orderStats.preparing || 0}}</text>
      <text class="stats-label">制作中</text>
    </view>
    <view class="stats-item" bindtap="onOrderTap" data-status="delivering">
      <text class="stats-number">{{orderStats.delivering || 0}}</text>
      <text class="stats-label">配送中</text>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <!-- 订单相关 -->
    <view class="menu-group">
      <view class="menu-item" bindtap="onOrderTap" data-status="all">
        <view class="menu-left">
          <icon class="menu-icon" type="success" size="20" color="#ff6b35"></icon>
          <text class="menu-text">我的订单</text>
        </view>
        <icon class="menu-arrow" type="arrow" size="16" color="#999"></icon>
      </view>
      
      <view class="menu-item" bindtap="onAddressTap">
        <view class="menu-left">
          <icon class="menu-icon" type="location" size="20" color="#ff6b35"></icon>
          <text class="menu-text">收货地址</text>
        </view>
        <icon class="menu-arrow" type="arrow" size="16" color="#999"></icon>
      </view>
      
      <view class="menu-item" bindtap="onCouponTap">
        <view class="menu-left">
          <icon class="menu-icon" type="success" size="20" color="#ff6b35"></icon>
          <text class="menu-text">优惠券</text>
        </view>
        <view class="menu-right">
          <text class="menu-badge" wx:if="{{couponCount > 0}}">{{couponCount}}</text>
          <icon class="menu-arrow" type="arrow" size="16" color="#999"></icon>
        </view>
      </view>
    </view>

    <!-- 服务相关 -->
    <view class="menu-group">
      <view class="menu-item" bindtap="onFavoriteTap">
        <view class="menu-left">
          <icon class="menu-icon" type="success" size="20" color="#ff6b35"></icon>
          <text class="menu-text">我的收藏</text>
        </view>
        <icon class="menu-arrow" type="arrow" size="16" color="#999"></icon>
      </view>
      
      <view class="menu-item" bindtap="onHistoryTap">
        <view class="menu-left">
          <icon class="menu-icon" type="clock" size="20" color="#ff6b35"></icon>
          <text class="menu-text">浏览历史</text>
        </view>
        <icon class="menu-arrow" type="arrow" size="16" color="#999"></icon>
      </view>
      
      <view class="menu-item" bindtap="onServiceTap">
        <view class="menu-left">
          <icon class="menu-icon" type="info" size="20" color="#ff6b35"></icon>
          <text class="menu-text">客服中心</text>
        </view>
        <icon class="menu-arrow" type="arrow" size="16" color="#999"></icon>
      </view>
    </view>

    <!-- 设置相关 -->
    <view class="menu-group">
      <view class="menu-item" bindtap="onSettingTap">
        <view class="menu-left">
          <icon class="menu-icon" type="setting" size="20" color="#ff6b35"></icon>
          <text class="menu-text">设置</text>
        </view>
        <icon class="menu-arrow" type="arrow" size="16" color="#999"></icon>
      </view>
      
      <view class="menu-item" bindtap="onAboutTap">
        <view class="menu-left">
          <icon class="menu-icon" type="info" size="20" color="#ff6b35"></icon>
          <text class="menu-text">关于我们</text>
        </view>
        <icon class="menu-arrow" type="arrow" size="16" color="#999"></icon>
      </view>
    </view>

    <!-- 管理员功能 -->
    <view class="menu-group" wx:if="{{isAdmin}}">
      <view class="menu-item" bindtap="onDishManageTap">
        <view class="menu-left">
          <icon class="menu-icon" type="success" size="20" color="#ff6b35"></icon>
          <text class="menu-text">菜品管理</text>
        </view>
        <icon class="menu-arrow" type="arrow" size="16" color="#999"></icon>
      </view>
      
      <view class="menu-item" bindtap="onOrderManageTap">
        <view class="menu-left">
          <icon class="menu-icon" type="success" size="20" color="#ff6b35"></icon>
          <text class="menu-text">订单管理</text>
        </view>
        <icon class="menu-arrow" type="arrow" size="16" color="#999"></icon>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="menu-group" wx:if="{{hasLogin}}">
      <view class="menu-item logout" bindtap="onLogout">
        <view class="menu-left">
          <icon class="menu-icon" type="clear" size="20" color="#ff4757"></icon>
          <text class="menu-text logout-text">退出登录</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text class="version-text">美食点餐 v1.0.0</text>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area"></view>
</view>
