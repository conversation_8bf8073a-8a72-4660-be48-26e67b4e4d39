/* components/star-rating/star-rating.wxss */
.star-rating {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.stars-container {
  display: flex;
  gap: 4rpx;
}

.star-item {
  position: relative;
  display: inline-block;
  cursor: pointer;
  transition: all 0.3s ease;
}

.star-item:active {
  transform: scale(0.9);
}

.star-bg {
  color: #e0e0e0;
  transition: all 0.3s ease;
}

.star-fill {
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.star-fill.full {
  color: #ffd700;
  text-shadow: 0 0 10rpx rgba(255, 215, 0, 0.5);
}

.star-fill.half {
  color: #ffd700;
  width: 50%;
  text-shadow: 0 0 10rpx rgba(255, 215, 0, 0.5);
}

.star-half-mask {
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 100%;
  background: transparent;
}

/* 尺寸变体 */
.star-rating-small .star-bg,
.star-rating-small .star-fill {
  font-size: 24rpx;
}

.star-rating-medium .star-bg,
.star-rating-medium .star-fill {
  font-size: 32rpx;
}

.star-rating-large .star-bg,
.star-rating-large .star-fill {
  font-size: 40rpx;
}

/* 评分信息 */
.rating-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.rating-number {
  font-size: 28rpx;
  font-weight: bold;
  color: #ffd700;
}

.rating-text {
  font-size: 24rpx;
  color: #666;
}

/* 小尺寸的评分信息 */
.star-rating-small .rating-number {
  font-size: 24rpx;
}

.star-rating-small .rating-text {
  font-size: 20rpx;
}

/* 大尺寸的评分信息 */
.star-rating-large .rating-number {
  font-size: 32rpx;
}

.star-rating-large .rating-text {
  font-size: 28rpx;
}

/* 悬停效果（仅在非只读模式下） */
.star-rating:not(.readonly) .star-item:hover .star-bg {
  color: #ffd700;
  transform: scale(1.1);
}

/* 动画效果 */
@keyframes starGlow {
  0% {
    text-shadow: 0 0 5rpx rgba(255, 215, 0, 0.5);
  }
  50% {
    text-shadow: 0 0 20rpx rgba(255, 215, 0, 0.8);
  }
  100% {
    text-shadow: 0 0 5rpx rgba(255, 215, 0, 0.5);
  }
}

.star-fill.full {
  animation: starGlow 2s ease-in-out infinite;
}
