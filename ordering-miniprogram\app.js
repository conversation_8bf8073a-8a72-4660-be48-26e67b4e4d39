// app.js
App({
  globalData: {
    // 后端服务地址
    baseUrl: 'http://localhost:8080/api',
    
    // 用户信息
    userInfo: null,
    hasLogin: false,
    
    // 购物车数据
    cart: [],
    cartTotal: 0,
    
    // 当前选择的地址
    selectedAddress: null,
    
    // 系统配置
    config: {
      deliveryFee: 5, // 配送费
      minOrderAmount: 20, // 最低起送金额
      businessHours: {
        start: '09:00',
        end: '22:00'
      }
    }
  },

  onLaunch() {
    console.log('🍽️ 在线点餐小程序启动')
    
    // 检查登录状态
    this.checkLoginStatus()
    
    // 初始化购物车
    this.initCart()
    
    // 检查更新
    this.checkForUpdate()
  },

  onShow() {
    console.log('小程序显示')
  },

  onHide() {
    console.log('小程序隐藏')
    // 保存购物车数据
    this.saveCart()
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.globalData.userInfo = userInfo
      this.globalData.hasLogin = true
    }
  },

  // 初始化购物车
  initCart() {
    try {
      const cart = wx.getStorageSync('cart')
      if (cart) {
        this.globalData.cart = cart
        this.calculateCartTotal()
      }
    } catch (error) {
      console.error('初始化购物车失败:', error)
    }
  },

  // 保存购物车
  saveCart() {
    try {
      wx.setStorageSync('cart', this.globalData.cart)
    } catch (error) {
      console.error('保存购物车失败:', error)
    }
  },

  // 添加到购物车
  addToCart(dish, quantity = 1) {
    const existingItem = this.globalData.cart.find(item => item.id === dish.id)
    
    if (existingItem) {
      existingItem.quantity += quantity
    } else {
      this.globalData.cart.push({
        ...dish,
        quantity: quantity
      })
    }
    
    this.calculateCartTotal()
    this.saveCart()
    
    // 显示提示
    wx.showToast({
      title: '已添加到购物车',
      icon: 'success',
      duration: 1500
    })
  },

  // 从购物车移除
  removeFromCart(dishId) {
    this.globalData.cart = this.globalData.cart.filter(item => item.id !== dishId)
    this.calculateCartTotal()
    this.saveCart()
  },

  // 更新购物车数量
  updateCartQuantity(dishId, quantity) {
    const item = this.globalData.cart.find(item => item.id === dishId)
    if (item) {
      if (quantity <= 0) {
        this.removeFromCart(dishId)
      } else {
        item.quantity = quantity
        this.calculateCartTotal()
        this.saveCart()
      }
    }
  },

  // 计算购物车总价
  calculateCartTotal() {
    this.globalData.cartTotal = this.globalData.cart.reduce((total, item) => {
      return total + (item.price * item.quantity)
    }, 0)
  },

  // 清空购物车
  clearCart() {
    this.globalData.cart = []
    this.globalData.cartTotal = 0
    this.saveCart()
  },

  // 获取购物车商品数量
  getCartItemCount() {
    return this.globalData.cart.reduce((count, item) => count + item.quantity, 0)
  },

  // 检查小程序更新
  checkForUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          console.log('发现新版本')
        }
      })
      
      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })
      
      updateManager.onUpdateFailed(() => {
        console.error('新版本下载失败')
      })
    }
  },

  // 用户登录
  login(userInfo) {
    this.globalData.userInfo = userInfo
    this.globalData.hasLogin = true
    
    try {
      wx.setStorageSync('userInfo', userInfo)
    } catch (error) {
      console.error('保存用户信息失败:', error)
    }
  },

  // 用户登出
  logout() {
    this.globalData.userInfo = null
    this.globalData.hasLogin = false
    
    try {
      wx.removeStorageSync('userInfo')
      wx.removeStorageSync('cart')
    } catch (error) {
      console.error('清除用户数据失败:', error)
    }
    
    this.clearCart()
  },

  // 格式化价格
  formatPrice(price) {
    return parseFloat(price).toFixed(2)
  },

  // 格式化时间
  formatTime(date) {
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    const day = date.getDate()
    const hour = date.getHours()
    const minute = date.getMinutes()
    const second = date.getSeconds()

    return `${[year, month, day].map(this.formatNumber).join('-')} ${[hour, minute, second].map(this.formatNumber).join(':')}`
  },

  formatNumber(n) {
    n = n.toString()
    return n[1] ? n : `0${n}`
  }
})
