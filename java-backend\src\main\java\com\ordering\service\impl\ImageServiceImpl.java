package com.ordering.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ordering.config.FileUploadConfig;
import com.ordering.entity.Image;
import com.ordering.mapper.ImageMapper;
import com.ordering.service.ImageService;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 图片服务实现类
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
@Service
@Transactional
public class ImageServiceImpl extends ServiceImpl<ImageMapper, Image> implements ImageService {

    @Autowired
    private FileUploadConfig fileUploadConfig;

    @Override
    public Image uploadImage(MultipartFile file, String category, Long businessId, String description) {
        log.info("开始上传图片: {}, 分类: {}, 业务ID: {}", file.getOriginalFilename(), category, businessId);

        // 验证文件
        if (!isValidImageType(file)) {
            throw new RuntimeException("不支持的图片格式");
        }
        if (!isValidFileSize(file)) {
            throw new RuntimeException("图片文件过大");
        }

        try {
            // 生成文件名和路径
            String originalFilename = file.getOriginalFilename();
            String extension = getFileExtension(originalFilename);
            String uniqueFileName = generateUniqueFileName(originalFilename);
            
            // 创建日期目录
            String dateDir = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
            String categoryDir = StringUtils.hasText(category) ? category : "other";
            String relativePath = categoryDir + "/" + dateDir + "/";
            
            // 确保目录存在
            File uploadDir = new File(fileUploadConfig.getPath() + relativePath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }

            // 保存原图
            String filePath = relativePath + uniqueFileName;
            File targetFile = new File(fileUploadConfig.getPath() + filePath);
            file.transferTo(targetFile);

            // 获取图片尺寸
            BufferedImage bufferedImage = ImageIO.read(targetFile);
            int width = bufferedImage.getWidth();
            int height = bufferedImage.getHeight();

            // 压缩图片（如果启用）
            if (fileUploadConfig.getCompress().getEnabled()) {
                compressImage(
                    targetFile.getAbsolutePath(),
                    targetFile.getAbsolutePath(),
                    fileUploadConfig.getCompress().getQuality(),
                    fileUploadConfig.getCompress().getMaxWidth(),
                    fileUploadConfig.getCompress().getMaxHeight()
                );
            }

            // 生成缩略图
            String thumbnailFileName = "thumb_" + uniqueFileName;
            String thumbnailPath = relativePath + thumbnailFileName;
            File thumbnailFile = new File(fileUploadConfig.getPath() + thumbnailPath);
            
            generateThumbnail(
                targetFile.getAbsolutePath(),
                thumbnailFile.getAbsolutePath(),
                fileUploadConfig.getCompress().getThumbnail().getWidth(),
                fileUploadConfig.getCompress().getThumbnail().getHeight()
            );

            // 构建访问URL
            String baseUrl = "/uploads/";
            String url = baseUrl + filePath;
            String thumbnailUrl = baseUrl + thumbnailPath;

            // 创建图片记录
            Image image = new Image()
                    .setName(FilenameUtils.getBaseName(originalFilename))
                    .setOriginalName(originalFilename)
                    .setFilePath(filePath)
                    .setUrl(url)
                    .setThumbnailPath(thumbnailPath)
                    .setThumbnailUrl(thumbnailUrl)
                    .setFileSize(file.getSize())
                    .setWidth(width)
                    .setHeight(height)
                    .setContentType(file.getContentType())
                    .setExtension(extension)
                    .setCategory(category)
                    .setBusinessId(businessId)
                    .setDescription(description)
                    .setSortOrder(0)
                    .setStatus(1)
                    .setCreatedAt(LocalDateTime.now())
                    .setUpdatedAt(LocalDateTime.now());

            // 保存到数据库
            save(image);

            log.info("图片上传成功: ID={}, URL={}", image.getId(), image.getUrl());
            return image;

        } catch (IOException e) {
            log.error("图片上传失败: {}", e.getMessage(), e);
            throw new RuntimeException("图片上传失败: " + e.getMessage());
        }
    }

    @Override
    public List<Image> uploadImages(MultipartFile[] files, String category, Long businessId, String description) {
        List<Image> images = new ArrayList<>();
        for (MultipartFile file : files) {
            if (!file.isEmpty()) {
                Image image = uploadImage(file, category, businessId, description);
                images.add(image);
            }
        }
        return images;
    }

    @Override
    public IPage<Image> getImagesByCategory(String category, int page, int size) {
        Page<Image> pageParam = new Page<>(page, size);
        QueryWrapper<Image> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("category", category)
                   .eq("status", 1)
                   .orderByDesc("created_at");
        return page(pageParam, queryWrapper);
    }

    @Override
    public List<Image> getImagesByBusinessId(Long businessId) {
        QueryWrapper<Image> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("business_id", businessId)
                   .eq("status", 1)
                   .orderByAsc("sort_order")
                   .orderByDesc("created_at");
        return list(queryWrapper);
    }

    @Override
    public List<Image> getImagesByCategoryAndBusinessId(String category, Long businessId) {
        QueryWrapper<Image> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("category", category)
                   .eq("business_id", businessId)
                   .eq("status", 1)
                   .orderByAsc("sort_order")
                   .orderByDesc("created_at");
        return list(queryWrapper);
    }

    @Override
    public boolean deleteImage(Long id) {
        Image image = getById(id);
        if (image == null) {
            return false;
        }

        // 删除物理文件
        deletePhysicalFile(image.getFilePath());
        deletePhysicalFile(image.getThumbnailPath());

        // 删除数据库记录
        return removeById(id);
    }

    @Override
    public int deleteImages(List<Long> ids) {
        int count = 0;
        for (Long id : ids) {
            if (deleteImage(id)) {
                count++;
            }
        }
        return count;
    }

    @Override
    public boolean updateImageInfo(Long id, String name, String description, Integer sortOrder) {
        Image image = new Image()
                .setId(id)
                .setName(name)
                .setDescription(description)
                .setSortOrder(sortOrder)
                .setUpdatedAt(LocalDateTime.now());
        return updateById(image);
    }

    @Override
    public Image getImageDetail(Long id) {
        return getById(id);
    }

    @Override
    public Image getImageByUrl(String url) {
        QueryWrapper<Image> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("url", url).eq("status", 1);
        return getOne(queryWrapper);
    }

    @Override
    public boolean compressImage(String sourceFile, String targetFile, double quality, int maxWidth, int maxHeight) {
        try {
            Thumbnails.of(sourceFile)
                    .size(maxWidth, maxHeight)
                    .outputQuality(quality)
                    .toFile(targetFile);
            return true;
        } catch (IOException e) {
            log.error("图片压缩失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean generateThumbnail(String sourceFile, String thumbnailFile, int width, int height) {
        try {
            Thumbnails.of(sourceFile)
                    .size(width, height)
                    .outputQuality(0.8)
                    .toFile(thumbnailFile);
            return true;
        } catch (IOException e) {
            log.error("缩略图生成失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean isValidImageType(MultipartFile file) {
        String extension = getFileExtension(file.getOriginalFilename());
        return fileUploadConfig.getAllowedTypes().contains(extension.toLowerCase());
    }

    @Override
    public boolean isValidFileSize(MultipartFile file) {
        return file.getSize() <= fileUploadConfig.getMaxSize();
    }

    @Override
    public String generateUniqueFileName(String originalFilename) {
        String extension = getFileExtension(originalFilename);
        return UUID.randomUUID().toString().replace("-", "") + "." + extension;
    }

    @Override
    public String getFileExtension(String filename) {
        return FilenameUtils.getExtension(filename);
    }

    /**
     * 删除物理文件
     */
    private void deletePhysicalFile(String filePath) {
        if (StringUtils.hasText(filePath)) {
            File file = new File(fileUploadConfig.getPath() + filePath);
            if (file.exists()) {
                file.delete();
            }
        }
    }
}
