<!--pages/index/index.wxml-->
<view class="container">
  <!-- 测试区域 -->
  <view class="test-section">
    <text class="test-title">🍽️ 美食点餐小程序</text>
    <text class="test-desc">欢迎使用在线点餐系统</text>
    <button class="test-btn" bindtap="onTestTap">测试功能</button>
  </view>

  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input" bindtap="onSearchTap">
      <icon class="search-icon" type="search" size="16" color="#999"></icon>
      <text class="search-placeholder">搜索菜品</text>
    </view>
  </view>

  <!-- 轮播图 -->
  <view class="banner-section" wx:if="{{banners.length > 0}}">
    <swiper 
      class="banner-swiper" 
      indicator-dots="{{true}}" 
      autoplay="{{true}}" 
      interval="{{3000}}" 
      duration="{{500}}"
      indicator-color="rgba(255,255,255,0.5)"
      indicator-active-color="#ff6b35"
    >
      <swiper-item wx:for="{{banners}}" wx:key="id">
        <image 
          class="banner-image" 
          src="{{item.image}}" 
          mode="aspectFill"
          bindtap="onBannerTap"
          data-banner="{{item}}"
        />
      </swiper-item>
    </swiper>
  </view>

  <!-- 分类导航 -->
  <view class="category-section">
    <view class="section-title">
      <text class="title-text">菜品分类</text>
    </view>
    <scroll-view class="category-scroll" scroll-x="{{true}}" show-scrollbar="{{false}}">
      <view class="category-list">
        <view 
          class="category-item {{selectedCategory === item.id ? 'active' : ''}}" 
          wx:for="{{categories}}" 
          wx:key="id"
          bindtap="onCategoryTap"
          data-category="{{item}}"
        >
          <image class="category-icon" src="{{item.icon}}" mode="aspectFit" />
          <text class="category-name">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 推荐菜品 -->
  <view class="recommend-section">
    <view class="section-title">
      <text class="title-text">今日推荐</text>
      <text class="more-text" bindtap="onMoreRecommendTap">更多 ></text>
    </view>
    <view class="dish-grid">
      <view 
        class="dish-card" 
        wx:for="{{recommendDishes}}" 
        wx:key="id"
        bindtap="onDishTap"
        data-dish="{{item}}"
      >
        <image class="dish-image" src="{{item.image}}" mode="aspectFill" />
        <view class="dish-info">
          <text class="dish-name">{{item.name}}</text>
          <text class="dish-desc">{{item.description}}</text>
          <view class="dish-footer">
            <text class="dish-price">
              <text class="price-symbol">¥</text>{{item.price}}
            </text>
            <view class="add-btn" bindtap="onAddToCartTap" data-dish="{{item}}">
              <icon type="plus" size="16" color="#fff"></icon>
            </view>
          </view>
        </view>
        <!-- 标签 -->
        <view class="dish-tags">
          <text class="tag tag-hot" wx:if="{{item.isHot}}">热</text>
          <text class="tag tag-new" wx:if="{{item.isNew}}">新</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 热门菜品 -->
  <view class="hot-section">
    <view class="section-title">
      <text class="title-text">热门菜品</text>
      <text class="more-text" bindtap="onMoreHotTap">更多 ></text>
    </view>
    <view class="hot-list">
      <view 
        class="hot-item" 
        wx:for="{{hotDishes}}" 
        wx:key="id"
        bindtap="onDishTap"
        data-dish="{{item}}"
      >
        <image class="hot-image" src="{{item.image}}" mode="aspectFill" />
        <view class="hot-info">
          <text class="hot-name">{{item.name}}</text>
          <text class="hot-desc">{{item.description}}</text>
          <view class="hot-meta">
            <text class="hot-sales">月销 {{item.salesCount}}</text>
            <text class="hot-rating">★ {{item.rating}}</text>
          </view>
        </view>
        <view class="hot-price">
          <text class="price-symbol">¥</text>{{item.price}}
        </view>
        <view class="hot-add" bindtap="onAddToCartTap" data-dish="{{item}}">
          <icon type="plus" size="14" color="#ff6b35"></icon>
        </view>
      </view>
    </view>
  </view>

  <!-- 新品菜品 -->
  <view class="new-section" wx:if="{{newDishes.length > 0}}">
    <view class="section-title">
      <text class="title-text">新品上市</text>
    </view>
    <scroll-view class="new-scroll" scroll-x="{{true}}" show-scrollbar="{{false}}">
      <view class="new-list">
        <view 
          class="new-item" 
          wx:for="{{newDishes}}" 
          wx:key="id"
          bindtap="onDishTap"
          data-dish="{{item}}"
        >
          <image class="new-image" src="{{item.image}}" mode="aspectFill" />
          <view class="new-info">
            <text class="new-name">{{item.name}}</text>
            <text class="new-price">
              <text class="price-symbol">¥</text>{{item.price}}
            </text>
          </view>
          <view class="new-tag">NEW</view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area"></view>
</view>

<!-- 购物车悬浮按钮 -->
<view class="cart-float" wx:if="{{cartCount > 0}}" bindtap="onCartTap">
  <view class="cart-icon">
    <icon type="success" size="20" color="#fff"></icon>
    <text class="cart-count">{{cartCount}}</text>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading" wx:if="{{loading}}">
  <text>加载中...</text>
</view>
