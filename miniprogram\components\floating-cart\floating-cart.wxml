<!--components/floating-cart/floating-cart.wxml-->
<view 
  class="floating-cart {{show ? 'show' : 'hide'}}" 
  animation="{{animationData}}"
  wx:if="{{cartCount > 0}}"
>
  <view class="cart-content">
    <!-- 购物车图标和数量 -->
    <view class="cart-icon-wrapper" bindtap="onCartTap" animation="{{bounceAnimation}}">
      <view class="cart-icon">
        <text class="icon">🛒</text>
        <view class="cart-badge" wx:if="{{cartCount > 0}}">
          <text class="badge-text">{{cartCount > 99 ? '99+' : cartCount}}</text>
        </view>
      </view>
    </view>

    <!-- 价格信息 -->
    <view class="price-info">
      <text class="price-label">合计</text>
      <text class="price-value">¥{{totalPrice.toFixed(2)}}</text>
    </view>

    <!-- 结算按钮 -->
    <view class="checkout-btn" bindtap="onCheckoutTap">
      <text class="checkout-text">去结算</text>
    </view>
  </view>

  <!-- 背景光效 -->
  <view class="glow-effect"></view>
</view>
