/* pages/cart/cart.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

/* 购物车内容 */
.cart-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-bottom: 120rpx;
}

/* 顶部操作栏 */
.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.cart-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2f3542;
}

.header-right {
  display: flex;
  gap: 30rpx;
}

.edit-btn, .clear-btn {
  font-size: 28rpx;
  color: #ff6b35;
  padding: 10rpx 20rpx;
  border: 1rpx solid #ff6b35;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.edit-btn:active, .clear-btn:active {
  background: #ff6b35;
  color: white;
}

/* 商品列表 */
.cart-list {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.cart-item:active {
  background: rgba(255, 107, 53, 0.05);
}

/* 选择框 */
.item-checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  transition: all 0.3s ease;
}

.item-checkbox.checked {
  background: #ff6b35;
  border-color: #ff6b35;
}

.checkbox-icon {
  font-size: 24rpx;
  color: white;
  font-weight: bold;
}

/* 商品信息 */
.item-content {
  flex: 1;
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.item-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 30rpx;
  color: #2f3542;
  font-weight: 500;
  display: block;
  margin-bottom: 10rpx;
  line-height: 1.3;
}

.item-price {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 数量控制 */
.quantity-controls {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.quantity-btn {
  width: 50rpx;
  height: 50rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.quantity-btn:active {
  background: rgba(0, 0, 0, 0.2);
  transform: scale(0.9);
}

.quantity-btn.increase {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
}

.btn-icon {
  font-size: 28rpx;
  color: #666;
  font-weight: bold;
}

.quantity-btn.increase .btn-icon {
  color: white;
}

.quantity-input {
  width: 80rpx;
  height: 50rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #2f3542;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8rpx;
}

/* 删除按钮 */
.delete-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 71, 87, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  transition: all 0.3s ease;
}

.delete-btn:active {
  background: rgba(255, 71, 87, 0.2);
  transform: scale(0.9);
}

.delete-icon {
  font-size: 28rpx;
}

/* 底部结算栏 */
.cart-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  z-index: 100;
}

.edit-footer, .checkout-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.select-all {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.select-checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.select-checkbox.checked {
  background: #ff6b35;
  border-color: #ff6b35;
}

.select-text {
  font-size: 28rpx;
  color: #2f3542;
}

.delete-selected-btn {
  background: rgba(255, 71, 87, 0.1);
  color: #ff4757;
  border: 2rpx solid #ff4757;
  border-radius: 40rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.delete-selected-btn[disabled] {
  background: rgba(0, 0, 0, 0.05);
  color: #ccc;
  border-color: #ccc;
}

.price-info {
  display: flex;
  align-items: baseline;
  gap: 10rpx;
}

.total-text {
  font-size: 28rpx;
  color: #666;
}

.total-price {
  font-size: 36rpx;
  color: #ff6b35;
  font-weight: bold;
}

.checkout-btn {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 53, 0.4);
}

.checkout-btn[disabled] {
  background: #ccc;
  box-shadow: none;
}

/* 空购物车 */
.empty-cart {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}

.empty-content {
  text-align: center;
  color: white;
}

.empty-image {
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 40rpx;
  opacity: 0.8;
}

.empty-title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.empty-subtitle {
  font-size: 28rpx;
  opacity: 0.8;
  display: block;
  margin-bottom: 60rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

.empty-actions {
  display: flex;
  gap: 30rpx;
  justify-content: center;
}

.continue-btn, .recommend-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #ff6b35;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  border-radius: 50rpx;
  padding: 25rpx 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.continue-btn:active, .recommend-btn:active {
  background: #ff6b35;
  color: white;
  transform: scale(0.95);
}
