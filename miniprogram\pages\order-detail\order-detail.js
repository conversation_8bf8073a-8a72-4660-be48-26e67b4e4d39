// pages/order-detail/order-detail.js
const { get, put } = require('../../utils/request')
const { showLoading, hideLoading, formatTime, showConfirm } = require('../../utils/util')

Page({
  data: {
    order: null,
    loading: true,
    statusMap: {
      'pending': '待确认',
      'confirmed': '已确认',
      'preparing': '制作中',
      'completed': '已完成',
      'cancelled': '已取消'
    },
    statusColorMap: {
      'pending': '#ff9500',
      'confirmed': '#007aff',
      'preparing': '#ff6b35',
      'completed': '#2ed573',
      'cancelled': '#999'
    }
  },

  onLoad(options) {
    const { id } = options
    if (id) {
      this.loadOrderDetail(id)
    }
  },

  onShow() {
    // 如果页面已经加载过，刷新数据
    if (this.data.order) {
      this.loadOrderDetail(this.data.order.id)
    }
  },

  onPullDownRefresh() {
    if (this.data.order) {
      this.loadOrderDetail(this.data.order.id).then(() => {
        wx.stopPullDownRefresh()
      })
    }
  },

  // 加载订单详情
  async loadOrderDetail(id) {
    try {
      this.setData({ loading: true })
      showLoading('加载中...')
      
      const res = await get(`/orders/${id}`)
      
      this.setData({
        order: res.data,
        loading: false
      })
      
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: `订单详情 - ${res.data.order_no}`
      })
    } catch (error) {
      console.error('加载订单详情失败:', error)
      this.setData({ loading: false })
      
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
      
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    } finally {
      hideLoading()
    }
  },

  // 取消订单
  async onCancelOrder() {
    const { order } = this.data
    
    if (!order || order.status !== 'pending') {
      wx.showToast({
        title: '当前状态不能取消',
        icon: 'none'
      })
      return
    }
    
    try {
      await showConfirm('确定要取消这个订单吗？')
      
      showLoading('取消中...')
      
      await put(`/orders/${order.id}/cancel`)
      
      wx.showToast({
        title: '订单已取消',
        icon: 'success'
      })
      
      // 刷新订单状态
      this.loadOrderDetail(order.id)
      
    } catch (error) {
      if (error !== false) { // 不是用户取消
        console.error('取消订单失败:', error)
        wx.showToast({
          title: '取消失败',
          icon: 'none'
        })
      }
    } finally {
      hideLoading()
    }
  },

  // 再来一单
  onReorder() {
    const { order } = this.data
    
    if (!order || !order.items) {
      wx.showToast({
        title: '订单信息异常',
        icon: 'none'
      })
      return
    }
    
    const app = getApp()
    
    // 清空购物车
    app.clearCart()
    
    // 添加订单商品到购物车
    order.items.forEach(item => {
      app.addToCart({
        id: item.dish_id,
        name: item.dish_name,
        price: item.dish_price,
        image: item.dish_image,
        quantity: item.quantity
      })
    })
    
    wx.showToast({
      title: '已添加到购物车',
      icon: 'success'
    })
    
    // 跳转到购物车
    setTimeout(() => {
      wx.switchTab({
        url: '/pages/cart/cart'
      })
    }, 1500)
  },

  // 联系客服
  onContactService() {
    wx.makePhoneCall({
      phoneNumber: '************'
    })
  },

  // 复制订单号
  onCopyOrderNo() {
    const { order } = this.data
    
    if (order && order.order_no) {
      wx.setClipboardData({
        data: order.order_no,
        success: () => {
          wx.showToast({
            title: '订单号已复制',
            icon: 'success'
          })
        }
      })
    }
  },

  // 查看菜品详情
  onDishTap(e) {
    const { item } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/detail/detail?id=${item.dish_id}`
    })
  },

  // 分享订单
  onShareAppMessage() {
    const { order } = this.data
    return {
      title: `我在在线点餐下了一单，快来看看吧！`,
      path: `/pages/order-detail/order-detail?id=${order?.id}`,
      imageUrl: '/images/share.jpg'
    }
  },

  // 格式化时间
  formatTime(time) {
    return formatTime(new Date(time))
  },

  // 获取图片URL
  getImageUrl(path) {
    const { getImageUrl } = require('../../utils/util')
    return getImageUrl(path)
  }
})
