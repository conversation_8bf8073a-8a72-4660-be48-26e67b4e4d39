/* pages/detail/detail.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 120rpx;
}

/* 图片区域 */
.image-section {
  position: relative;
  height: 600rpx;
  background: white;
}

.image-swiper {
  width: 100%;
  height: 100%;
}

.dish-image {
  width: 100%;
  height: 100%;
}

.back-btn {
  position: absolute;
  top: 60rpx;
  left: 30rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10rpx);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  transition: all 0.3s ease;
}

.back-btn:active {
  background: rgba(0, 0, 0, 0.7);
  transform: scale(0.9);
}

.back-icon {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
}

.like-btn {
  position: absolute;
  top: 60rpx;
  right: 30rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  transition: all 0.3s ease;
}

.like-btn:active {
  transform: scale(0.9);
}

.like-btn.liked {
  background: rgba(255, 107, 53, 0.9);
}

.like-icon {
  font-size: 36rpx;
}

.image-indicator {
  position: absolute;
  bottom: 20rpx;
  right: 30rpx;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.indicator-text {
  font-size: 24rpx;
}

/* 信息区域 */
.info-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  margin: 20rpx;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.dish-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.dish-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #2f3542;
  flex: 1;
  line-height: 1.3;
}

.dish-tags {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-left: 20rpx;
}

.tag {
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  color: white;
  font-weight: bold;
  text-align: center;
}

.tag.hot {
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
}

.tag.new {
  background: linear-gradient(135deg, #2ed573 0%, #17c0eb 100%);
}

.dish-desc {
  font-size: 28rpx;
  color: #57606f;
  line-height: 1.5;
  margin-bottom: 25rpx;
}

.dish-meta {
  display: flex;
  gap: 40rpx;
  margin-bottom: 25rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.meta-label {
  font-size: 24rpx;
  color: #999;
}

.meta-value {
  font-size: 26rpx;
  color: #2f3542;
  font-weight: 500;
}

.price-section {
  display: flex;
  align-items: baseline;
  gap: 20rpx;
}

.current-price {
  font-size: 48rpx;
  color: #ff6b35;
  font-weight: bold;
}

.original-price {
  font-size: 28rpx;
  color: #999;
  text-decoration: line-through;
}

/* 数量选择区域 */
.quantity-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  margin: 20rpx;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.section-title {
  margin-bottom: 25rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #2f3542;
}

.quantity-selector {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 53, 0.4);
  transition: all 0.3s ease;
}

.quantity-btn:active {
  transform: scale(0.9);
}

.quantity-btn.decrease {
  background: rgba(0, 0, 0, 0.1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.btn-text {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
}

.quantity-btn.decrease .btn-text {
  color: #666;
}

.quantity-input {
  width: 120rpx;
  height: 60rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #2f3542;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 12rpx;
}

/* 相关推荐区域 */
.related-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  margin: 20rpx;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.related-scroll {
  margin-top: 20rpx;
}

.related-list {
  display: flex;
  gap: 20rpx;
  padding-bottom: 10rpx;
}

.related-item {
  flex-shrink: 0;
  width: 200rpx;
  background: rgba(0, 0, 0, 0.03);
  border-radius: 16rpx;
  padding: 20rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.related-item:active {
  background: rgba(255, 107, 53, 0.1);
  transform: scale(0.95);
}

.related-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-bottom: 15rpx;
}

.related-name {
  font-size: 24rpx;
  color: #2f3542;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.related-price {
  font-size: 26rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20rpx;
  z-index: 100;
}

.cart-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.cart-info:active {
  transform: scale(0.95);
}

.cart-icon-wrapper {
  position: relative;
}

.cart-icon {
  font-size: 36rpx;
  color: #ff6b35;
}

.cart-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  min-width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
}

.cart-text {
  font-size: 22rpx;
  color: #666;
}

.action-buttons {
  flex: 1;
  display: flex;
  gap: 15rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.action-btn:active {
  transform: scale(0.98);
}

.add-cart {
  background: rgba(255, 107, 53, 0.1);
  color: #ff6b35;
  border: 2rpx solid #ff6b35;
}

.buy-now {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 53, 0.4);
}

.btn-price {
  font-size: 22rpx;
  margin-top: 4rpx;
}
