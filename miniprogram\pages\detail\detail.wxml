<!--pages/detail/detail.wxml-->
<view class="container" wx:if="{{!loading}}">
  <!-- 图片轮播 -->
  <view class="image-section">
    <swiper 
      class="image-swiper" 
      indicator-dots="{{imageList.length > 1}}"
      indicator-color="rgba(255, 255, 255, 0.5)"
      indicator-active-color="#ff6b35"
      autoplay="{{false}}"
      circular="{{true}}"
      bindchange="onSwiperChange"
    >
      <swiper-item 
        wx:for="{{imageList}}" 
        wx:key="*this"
        bindtap="onImageTap"
        data-index="{{index}}"
      >
        <image 
          class="dish-image" 
          src="{{item}}" 
          mode="aspectFill"
          lazy-load="{{true}}"
        />
      </swiper-item>
    </swiper>
    
    <!-- 返回按钮 -->
    <view class="back-btn" bindtap="onBack">
      <text class="back-icon">←</text>
    </view>
    
    <!-- 收藏按钮 -->
    <view class="like-btn {{isLiked ? 'liked' : ''}}" bindtap="onToggleLike">
      <text class="like-icon">{{isLiked ? '❤️' : '🤍'}}</text>
    </view>
    
    <!-- 图片指示器 -->
    <view class="image-indicator" wx:if="{{imageList.length > 1}}">
      <text class="indicator-text">{{currentImageIndex + 1}}/{{imageList.length}}</text>
    </view>
  </view>

  <!-- 菜品信息 -->
  <view class="info-section">
    <view class="basic-info">
      <view class="dish-header">
        <text class="dish-name">{{dish.name}}</text>
        <view class="dish-tags">
          <text class="tag hot" wx:if="{{dish.is_hot}}">🔥 热门</text>
          <text class="tag new" wx:if="{{dish.is_new}}">✨ 新品</text>
        </view>
      </view>
      
      <text class="dish-desc">{{dish.description}}</text>
      
      <view class="dish-meta">
        <view class="meta-item">
          <text class="meta-label">销量</text>
          <text class="meta-value">{{dish.sales_count || 0}}</text>
        </view>
        <view class="meta-item">
          <text class="meta-label">评分</text>
          <star-rating 
            rating="4.8" 
            size="small" 
            readonly="{{true}}"
            show-text="{{false}}"
          />
        </view>
      </view>
      
      <view class="price-section">
        <text class="current-price">¥{{dish.price}}</text>
        <text class="original-price" wx:if="{{dish.original_price}}">¥{{dish.original_price}}</text>
      </view>
    </view>
  </view>

  <!-- 数量选择 -->
  <view class="quantity-section">
    <view class="section-title">
      <text class="title-text">选择数量</text>
    </view>
    <view class="quantity-selector">
      <view class="quantity-btn decrease" bindtap="onQuantityDecrease">
        <text class="btn-text">-</text>
      </view>
      <input 
        class="quantity-input" 
        type="number" 
        value="{{quantity}}"
        bindinput="onQuantityInput"
      />
      <view class="quantity-btn increase" bindtap="onQuantityIncrease">
        <text class="btn-text">+</text>
      </view>
    </view>
  </view>

  <!-- 相关推荐 -->
  <view class="related-section" wx:if="{{relatedDishes.length > 0}}">
    <view class="section-title">
      <text class="title-text">相关推荐</text>
    </view>
    <scroll-view class="related-scroll" scroll-x="{{true}}" show-scrollbar="{{false}}">
      <view class="related-list">
        <view 
          class="related-item"
          wx:for="{{relatedDishes}}"
          wx:key="id"
          bindtap="onRelatedDishTap"
          data-dish="{{item}}"
        >
          <image 
            class="related-image" 
            src="{{item.image}}" 
            mode="aspectFill"
          />
          <text class="related-name">{{item.name}}</text>
          <text class="related-price">¥{{item.price}}</text>
        </view>
      </view>
    </scroll-view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-container" wx:if="{{loading}}">
  <loading-animation 
    show="{{loading}}"
    type="food"
    text="加载中..."
  />
</view>

<!-- 底部操作栏 -->
<view class="bottom-actions" wx:if="{{!loading}}">
  <view class="cart-info" bindtap="onViewCart">
    <view class="cart-icon-wrapper">
      <text class="cart-icon">🛒</text>
      <view class="cart-badge" wx:if="{{cartCount > 0}}">
        <text class="badge-text">{{cartCount}}</text>
      </view>
    </view>
    <text class="cart-text">购物车</text>
  </view>
  
  <view class="action-buttons">
    <button class="action-btn add-cart" bindtap="onAddToCart">
      <text class="btn-text">加入购物车</text>
      <text class="btn-price">¥{{totalPrice.toFixed(2)}}</text>
    </button>
    
    <button class="action-btn buy-now" bindtap="onBuyNow">
      <text class="btn-text">立即购买</text>
    </button>
  </view>
</view>
