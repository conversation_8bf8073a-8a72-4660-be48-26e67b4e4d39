/**app.wxss**/
/* 全局样式 */

/* 重置样式 */
page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'Segoe UI', Arial, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 通用容器 */
.container {
  padding: 20rpx;
}

.safe-area {
  padding-bottom: env(safe-area-inset-bottom);
}

/* 卡片样式 */
.card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
  overflow: hidden;
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.card-body {
  padding: 30rpx;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

.btn-primary {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: #fff;
}

.btn-primary:active {
  background: linear-gradient(135deg, #e55a2b, #de7f0e);
}

.btn-secondary {
  background: #f0f0f0;
  color: #666;
}

.btn-secondary:active {
  background: #e0e0e0;
}

.btn-outline {
  background: transparent;
  border: 2rpx solid #ff6b35;
  color: #ff6b35;
}

.btn-outline:active {
  background: #ff6b35;
  color: #fff;
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  border-radius: 40rpx;
}

.btn-large {
  padding: 28rpx 60rpx;
  font-size: 32rpx;
  border-radius: 60rpx;
}

.btn-block {
  width: 100%;
  display: flex;
}

.btn-disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 输入框样式 */
.input-group {
  margin-bottom: 30rpx;
}

.input-label {
  display: block;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.input {
  width: 100%;
  padding: 24rpx 30rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fff;
  transition: border-color 0.3s ease;
}

.input:focus {
  border-color: #ff6b35;
}

.input-error {
  border-color: #ff4757;
}

/* 列表样式 */
.list {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background-color: #f8f8f8;
}

.list-item-content {
  flex: 1;
  margin-left: 20rpx;
}

.list-item-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.list-item-desc {
  font-size: 24rpx;
  color: #999;
}

.list-item-extra {
  color: #999;
  font-size: 24rpx;
}

/* 图片样式 */
.image {
  display: block;
  width: 100%;
  height: auto;
}

.image-round {
  border-radius: 50%;
}

.image-rounded {
  border-radius: 12rpx;
}

/* 标签样式 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 500;
  margin-right: 12rpx;
  margin-bottom: 12rpx;
}

.tag-primary {
  background: rgba(255, 107, 53, 0.1);
  color: #ff6b35;
}

.tag-success {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.tag-warning {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.tag-danger {
  background: rgba(255, 71, 87, 0.1);
  color: #ff4757;
}

.tag-info {
  background: rgba(116, 185, 255, 0.1);
  color: #74b9ff;
}

/* 徽章样式 */
.badge {
  position: relative;
}

.badge::after {
  content: attr(data-badge);
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4757;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1;
}

/* 分割线 */
.divider {
  height: 1rpx;
  background: #f0f0f0;
  margin: 30rpx 0;
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  font-size: 28rpx;
  color: #999;
}

/* 价格样式 */
.price {
  color: #ff6b35;
  font-weight: 600;
}

.price-large {
  font-size: 36rpx;
}

.price-small {
  font-size: 24rpx;
}

.price-symbol {
  font-size: 0.8em;
  margin-right: 2rpx;
}

/* 数量控制器 */
.quantity-control {
  display: flex;
  align-items: center;
  border: 2rpx solid #e0e0e0;
  border-radius: 40rpx;
  overflow: hidden;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f8f8;
  color: #666;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.quantity-btn:active {
  background: #e0e0e0;
}

.quantity-btn.disabled {
  opacity: 0.3;
  pointer-events: none;
}

.quantity-input {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  font-size: 28rpx;
  border: none;
  background: #fff;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: #ff6b35; }
.text-success { color: #2ecc71; }
.text-warning { color: #ffc107; }
.text-danger { color: #ff4757; }
.text-info { color: #74b9ff; }
.text-muted { color: #999; }

.bg-primary { background-color: #ff6b35; }
.bg-success { background-color: #2ecc71; }
.bg-warning { background-color: #ffc107; }
.bg-danger { background-color: #ff4757; }
.bg-info { background-color: #74b9ff; }

.m-0 { margin: 0; }
.m-1 { margin: 10rpx; }
.m-2 { margin: 20rpx; }
.m-3 { margin: 30rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 10rpx; }
.p-2 { padding: 20rpx; }
.p-3 { padding: 30rpx; }

.flex { display: flex; }
.flex-1 { flex: 1; }
.flex-center { 
  display: flex; 
  align-items: center; 
  justify-content: center; 
}
.flex-between { 
  display: flex; 
  align-items: center; 
  justify-content: space-between; 
}
.flex-column { flex-direction: column; }

.hidden { display: none; }
.visible { display: block; }
