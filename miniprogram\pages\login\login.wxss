/* pages/login/login.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 30rpx;
}

/* 顶部装饰 */
.header-decoration {
  position: absolute;
  top: -100rpx;
  left: -100rpx;
  right: -100rpx;
  height: 300rpx;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 50rpx;
  left: 100rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 20rpx;
  right: 150rpx;
  animation-delay: 2s;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 100rpx;
  right: 50rpx;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20rpx) rotate(180deg);
  }
}

/* 返回按钮 */
.back-btn {
  position: absolute;
  top: 80rpx;
  left: 30rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  z-index: 10;
}

.back-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.9);
}

.back-icon {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
}

/* 登录卡片 */
.login-card {
  width: 100%;
  max-width: 600rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 40rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  z-index: 5;
}

/* 登录头部 */
.login-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(255, 107, 53, 0.4);
}

.logo-icon {
  font-size: 60rpx;
  color: white;
}

.app-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #2f3542;
  display: block;
  margin-bottom: 15rpx;
}

.app-slogan {
  font-size: 26rpx;
  color: #57606f;
  opacity: 0.8;
}

/* 登录方式切换 */
.login-tabs {
  display: flex;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 50rpx;
  padding: 8rpx;
  margin-bottom: 40rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border-radius: 40rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  box-shadow: 0 4rpx 15rpx rgba(255, 107, 53, 0.3);
}

.tab-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #666;
}

.tab-item.active .tab-text {
  color: white;
}

/* 表单样式 */
.login-form {
  margin-bottom: 40rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.input-wrapper {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.03);
  border: 2rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 25rpx;
  padding: 0 30rpx;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #ff6b35;
  background: rgba(255, 107, 53, 0.05);
}

.input-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  color: #ff6b35;
}

.form-input {
  flex: 1;
  height: 100rpx;
  font-size: 28rpx;
  color: #2f3542;
}

.code-btn {
  padding: 15rpx 25rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.code-btn.disabled {
  background: #ccc;
}

.code-text {
  font-size: 24rpx;
  color: white;
  font-weight: 500;
}

/* 登录按钮 */
.login-btn, .wechat-login-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 50rpx;
  border: none;
  box-shadow: 0 8rpx 25rpx rgba(255, 107, 53, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-btn:active, .wechat-login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(255, 107, 53, 0.4);
}

.login-btn.loading, .wechat-login-btn.loading {
  background: #ccc;
  box-shadow: none;
}

.btn-text {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
}

/* 微信登录特殊样式 */
.wechat-login-content {
  text-align: center;
  padding: 40rpx 0;
}

.wechat-icon {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #07c160 0%, #00ae42 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(7, 193, 96, 0.4);
}

.wechat-logo {
  font-size: 60rpx;
  color: white;
}

.wechat-tip {
  font-size: 28rpx;
  color: #57606f;
  margin-bottom: 40rpx;
  display: block;
}

.wechat-login-btn {
  background: linear-gradient(135deg, #07c160 0%, #00ae42 100%);
  box-shadow: 0 8rpx 25rpx rgba(7, 193, 96, 0.4);
}

/* 协议部分 */
.agreement-section {
  margin-top: 30rpx;
}

.agreement-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 15rpx;
}

.checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #ddd;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.checkbox.checked {
  background: #ff6b35;
  border-color: #ff6b35;
}

.checkbox-icon {
  font-size: 20rpx;
  color: white;
  font-weight: bold;
}

.agreement-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

.agreement-link {
  color: #ff6b35;
  text-decoration: underline;
}

/* 底部装饰 */
.footer-decoration {
  position: absolute;
  bottom: -100rpx;
  left: -100rpx;
  right: -100rpx;
  height: 300rpx;
}

.decoration-wave {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50% 50% 0 0;
  animation: wave 4s ease-in-out infinite;
}

.wave-1 {
  animation-delay: 0s;
  opacity: 0.3;
}

.wave-2 {
  animation-delay: 1s;
  opacity: 0.2;
  height: 80rpx;
}

.wave-3 {
  animation-delay: 2s;
  opacity: 0.1;
  height: 60rpx;
}

@keyframes wave {
  0%, 100% {
    transform: scaleX(1);
  }
  50% {
    transform: scaleX(1.1);
  }
}
