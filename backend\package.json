{"name": "ordering-system-backend", "version": "1.0.0", "description": "微信小程序在线点餐系统后端服务", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "express", "mysql", "wechat", "miniprogram", "ordering"], "author": "移动UI应用开发课程", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "cors": "^2.8.5", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "multer": "^1.4.5-lts.1", "moment": "^2.29.4", "dotenv": "^16.3.1", "express-rate-limit": "^6.10.0", "helmet": "^7.0.0", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}