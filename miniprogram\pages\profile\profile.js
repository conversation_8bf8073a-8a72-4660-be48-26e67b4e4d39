// pages/profile/profile.js
const { get } = require('../../utils/request')
const { showLoading, hideLoading, showConfirm, requireLogin } = require('../../utils/util')

Page({
  data: {
    userInfo: null,
    orderStats: {
      pending: 0,
      confirmed: 0,
      preparing: 0,
      completed: 0
    },
    showDebug: false,
    debugClickCount: 0
  },

  onLoad() {
    // 检查是否显示调试入口（连续点击头像5次）
    const debugMode = wx.getStorageSync('debugMode')
    this.setData({ showDebug: debugMode })
  },

  onShow() {
    this.loadUserInfo()
    this.loadOrderStats()
    this.updateCartCount()
  },

  // 加载用户信息
  loadUserInfo() {
    const app = getApp()
    const userInfo = app.getUserInfo()
    this.setData({ userInfo })
  },

  // 加载订单统计
  async loadOrderStats() {
    const app = getApp()
    const token = app.getToken()
    
    if (!token) return

    try {
      const res = await get('/orders/stats')
      this.setData({
        orderStats: res.data
      })
    } catch (error) {
      console.error('加载订单统计失败:', error)
    }
  },

  // 头像点击（调试模式激活）
  onAvatarTap() {
    const count = this.data.debugClickCount + 1
    this.setData({ debugClickCount: count })

    if (count >= 5) {
      this.setData({ 
        showDebug: true,
        debugClickCount: 0 
      })
      wx.setStorageSync('debugMode', true)
      wx.showToast({
        title: '调试模式已开启',
        icon: 'success'
      })
    }

    // 重置计数器
    setTimeout(() => {
      this.setData({ debugClickCount: 0 })
    }, 3000)
  },

  // 登录
  onLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    })
  },

  // 我的订单
  onMyOrders() {
    if (!requireLogin()) return
    
    wx.navigateTo({
      url: '/pages/order-list/order-list'
    })
  },

  // 订单状态点击
  onOrderStatusTap(e) {
    if (!requireLogin()) return
    
    const { status } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/order-list/order-list?status=${status}`
    })
  },

  // 我的收藏
  onMyFavorites() {
    if (!requireLogin()) return
    
    wx.navigateTo({
      url: '/pages/favorites/favorites'
    })
  },

  // 收货地址
  onMyAddress() {
    if (!requireLogin()) return
    
    wx.navigateTo({
      url: '/pages/address/address'
    })
  },

  // 优惠券
  onMyCoupons() {
    if (!requireLogin()) return
    
    wx.navigateTo({
      url: '/pages/coupons/coupons'
    })
  },

  // 客服
  onCustomerService() {
    wx.makePhoneCall({
      phoneNumber: '************'
    })
  },

  // 设置
  onSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    })
  },

  // 关于我们
  onAbout() {
    wx.showModal({
      title: '关于我们',
      content: '美食天下 v1.0.0\n\n一个专业的在线点餐平台，为您提供优质的美食服务。',
      showCancel: false
    })
  },

  // 系统调试
  onDebug() {
    wx.navigateTo({
      url: '/pages/debug/debug'
    })
  },

  // 退出登录
  async onLogout() {
    try {
      await showConfirm('确定要退出登录吗？')
      
      const app = getApp()
      app.clearUserInfo()
      app.clearCart()
      
      this.setData({
        userInfo: null,
        orderStats: {
          pending: 0,
          confirmed: 0,
          preparing: 0,
          completed: 0
        }
      })
      
      wx.showToast({
        title: '已退出登录',
        icon: 'success'
      })
      
    } catch (error) {
      // 用户取消
    }
  },

  // 更新购物车数量
  updateCartCount() {
    const app = getApp()
    const cartData = app.getCartData()
    const cartCount = cartData.cart.reduce((total, item) => total + item.quantity, 0)
    
    if (cartCount > 0) {
      wx.setTabBarBadge({
        index: 2,
        text: cartCount.toString()
      })
    } else {
      wx.removeTabBarBadge({
        index: 2
      })
    }
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '美食天下 - 发现身边的美味',
      path: '/pages/index/index',
      imageUrl: '/images/share.jpg'
    }
  }
})
