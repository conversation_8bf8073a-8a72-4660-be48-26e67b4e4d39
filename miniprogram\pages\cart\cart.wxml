<!--pages/cart/cart.wxml-->
<view class="container">
  <!-- 购物车有商品时 -->
  <view class="cart-content" wx:if="{{!isEmpty}}">
    <!-- 顶部操作栏 -->
    <view class="cart-header">
      <view class="header-left">
        <text class="cart-title">购物车 ({{cartItems.length}})</text>
      </view>
      <view class="header-right">
        <text class="edit-btn" bindtap="onToggleEditMode">
          {{isEditMode ? '完成' : '编辑'}}
        </text>
        <text class="clear-btn" bindtap="onClearCart">清空</text>
      </view>
    </view>

    <!-- 商品列表 -->
    <scroll-view class="cart-list" scroll-y="{{true}}">
      <view 
        class="cart-item"
        wx:for="{{cartItems}}"
        wx:key="id"
      >
        <!-- 选择框 -->
        <view 
          class="item-checkbox {{selectedItems.includes(item.id) ? 'checked' : ''}}"
          bindtap="onItemSelect"
          data-id="{{item.id}}"
        >
          <text class="checkbox-icon">{{selectedItems.includes(item.id) ? '✓' : ''}}</text>
        </view>

        <!-- 商品信息 -->
        <view class="item-content" bindtap="onItemTap" data-id="{{item.id}}">
          <image 
            class="item-image" 
            src="{{item.image || '/images/dish-default.jpg'}}" 
            mode="aspectFill"
          />
          <view class="item-info">
            <text class="item-name">{{item.name}}</text>
            <text class="item-price">¥{{item.price}}</text>
          </view>
        </view>

        <!-- 数量控制 -->
        <view class="quantity-controls">
          <view 
            class="quantity-btn decrease"
            bindtap="onQuantityDecrease"
            data-id="{{item.id}}"
          >
            <text class="btn-icon">-</text>
          </view>
          <input 
            class="quantity-input"
            type="number"
            value="{{item.quantity}}"
            bindinput="onQuantityInput"
            data-id="{{item.id}}"
          />
          <view 
            class="quantity-btn increase"
            bindtap="onQuantityIncrease"
            data-id="{{item.id}}"
          >
            <text class="btn-icon">+</text>
          </view>
        </view>

        <!-- 删除按钮 (编辑模式) -->
        <view 
          class="delete-btn"
          wx:if="{{isEditMode}}"
          bindtap="onRemoveItem"
          data-id="{{item.id}}"
        >
          <text class="delete-icon">🗑️</text>
        </view>
      </view>
    </scroll-view>

    <!-- 底部结算栏 -->
    <view class="cart-footer">
      <!-- 编辑模式 -->
      <view class="edit-footer" wx:if="{{isEditMode}}">
        <view class="select-all" bindtap="onSelectAll">
          <view class="select-checkbox {{isAllSelected ? 'checked' : ''}}">
            <text class="checkbox-icon">{{isAllSelected ? '✓' : ''}}</text>
          </view>
          <text class="select-text">全选</text>
        </view>
        <button 
          class="delete-selected-btn"
          bindtap="onRemoveSelected"
          disabled="{{selectedItems.length === 0}}"
        >
          删除选中 ({{selectedItems.length}})
        </button>
      </view>

      <!-- 结算模式 -->
      <view class="checkout-footer" wx:else>
        <view class="select-all" bindtap="onSelectAll">
          <view class="select-checkbox {{isAllSelected ? 'checked' : ''}}">
            <text class="checkbox-icon">{{isAllSelected ? '✓' : ''}}</text>
          </view>
          <text class="select-text">全选</text>
        </view>
        
        <view class="price-info">
          <text class="total-text">合计: </text>
          <text class="total-price">¥{{totalPrice.toFixed(2)}}</text>
        </view>
        
        <button 
          class="checkout-btn"
          bindtap="onCheckout"
          disabled="{{selectedItems.length === 0}}"
        >
          去结算 ({{totalCount}})
        </button>
      </view>
    </view>
  </view>

  <!-- 购物车为空时 -->
  <view class="empty-cart" wx:else>
    <view class="empty-content">
      <image class="empty-image" src="/images/empty-cart.png" mode="aspectFit"></image>
      <text class="empty-title">购物车空空如也</text>
      <text class="empty-subtitle">快去挑选喜欢的美食吧</text>
      
      <view class="empty-actions">
        <button class="continue-btn" bindtap="onContinueShopping">
          去逛逛
        </button>
        <button class="recommend-btn" bindtap="onRecommendTap">
          看推荐
        </button>
      </view>
    </view>
  </view>
</view>
