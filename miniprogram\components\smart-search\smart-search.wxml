<!--components/smart-search/smart-search.wxml-->
<view class="smart-search">
  <!-- 搜索输入框 -->
  <view class="search-input-container">
    <view class="search-input {{focused ? 'focused' : ''}}">
      <text class="search-icon">🔍</text>
      <input 
        class="input"
        placeholder="{{placeholder}}"
        value="{{searchValue}}"
        bindinput="onInput"
        bindfocus="onFocus"
        bindblur="onBlur"
        bindconfirm="onConfirm"
        confirm-type="search"
      />
      <view class="clear-btn" wx:if="{{searchValue}}" bindtap="onClear">
        <text class="clear-icon">✕</text>
      </view>
    </view>
  </view>

  <!-- 搜索面板 -->
  <view 
    class="search-panel {{focused ? 'show' : 'hide'}}" 
    animation="{{animationData}}"
    wx:if="{{focused}}"
  >
    <!-- 搜索建议 -->
    <view class="suggestions-section" wx:if="{{showSuggestions && suggestions.length > 0}}">
      <view class="section-title">
        <text class="title-icon">💡</text>
        <text class="title-text">搜索建议</text>
      </view>
      <view class="suggestions-list">
        <view 
          class="suggestion-item"
          wx:for="{{suggestions}}"
          wx:key="*this"
          bindtap="onSuggestionTap"
          data-keyword="{{item}}"
        >
          <text class="suggestion-icon">🔍</text>
          <text class="suggestion-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 搜索历史 -->
    <view class="history-section" wx:if="{{!showSuggestions && showHistory && searchHistory.length > 0}}">
      <view class="section-title">
        <text class="title-icon">🕒</text>
        <text class="title-text">搜索历史</text>
        <text class="clear-history" bindtap="onClearHistory">清除</text>
      </view>
      <view class="history-list">
        <view 
          class="history-item"
          wx:for="{{searchHistory}}"
          wx:key="*this"
          bindtap="onHistoryTap"
          data-keyword="{{item}}"
        >
          <text class="history-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 热门关键词 -->
    <view class="hot-keywords-section" wx:if="{{!showSuggestions && hotKeywords.length > 0}}">
      <view class="section-title">
        <text class="title-icon">🔥</text>
        <text class="title-text">热门搜索</text>
      </view>
      <view class="hot-keywords-list">
        <view 
          class="hot-keyword-item"
          wx:for="{{hotKeywords}}"
          wx:key="*this"
          bindtap="onHotKeywordTap"
          data-keyword="{{item}}"
        >
          <text class="hot-keyword-text">{{item}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 遮罩层 -->
  <view class="search-mask {{focused ? 'show' : 'hide'}}" wx:if="{{focused}}"></view>
</view>
