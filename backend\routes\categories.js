const express = require('express')
const { query } = require('../config/database')
const router = express.Router()

// 获取分类列表
router.get('/', async (req, res) => {
  try {
    const { include_dishes = false } = req.query

    let sql = `
      SELECT c.*, 
      (SELECT COUNT(*) FROM dishes d WHERE d.category_id = c.id AND d.status = 1) as dish_count
      FROM categories c 
      WHERE c.status = 1 
      ORDER BY c.sort_order DESC, c.id ASC
    `

    const categories = await query(sql)

    // 如果需要包含菜品信息
    if (include_dishes === 'true') {
      for (let category of categories) {
        const dishes = await query(`
          SELECT id, name, price, image, description, is_hot, is_new, sales_count
          FROM dishes 
          WHERE category_id = ? AND status = 1 
          ORDER BY sort_order DESC, sales_count DESC
          LIMIT 10
        `, [category.id])
        
        category.dishes = dishes
      }
    }

    res.json({
      code: 0,
      message: '获取成功',
      data: categories
    })
  } catch (error) {
    console.error('获取分类列表错误:', error)
    res.status(500).json({
      code: 500,
      message: '获取分类列表失败'
    })
  }
})

// 获取分类详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params

    const categories = await query(`
      SELECT c.*, 
      (SELECT COUNT(*) FROM dishes d WHERE d.category_id = c.id AND d.status = 1) as dish_count
      FROM categories c 
      WHERE c.id = ? AND c.status = 1
    `, [id])

    if (categories.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '分类不存在'
      })
    }

    const category = categories[0]

    // 获取该分类下的菜品
    const dishes = await query(`
      SELECT id, name, price, image, description, is_hot, is_new, sales_count
      FROM dishes 
      WHERE category_id = ? AND status = 1 
      ORDER BY sort_order DESC, sales_count DESC
    `, [id])

    category.dishes = dishes

    res.json({
      code: 0,
      message: '获取成功',
      data: category
    })
  } catch (error) {
    console.error('获取分类详情错误:', error)
    res.status(500).json({
      code: 500,
      message: '获取分类详情失败'
    })
  }
})

// 获取分类下的菜品
router.get('/:id/dishes', async (req, res) => {
  try {
    const { id } = req.params
    const { 
      page = 1, 
      limit = 20, 
      sort = 'sort_order',
      keyword 
    } = req.query

    // 验证分类是否存在
    const categories = await query(
      'SELECT id FROM categories WHERE id = ? AND status = 1',
      [id]
    )

    if (categories.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '分类不存在'
      })
    }

    let sql = `
      SELECT id, name, price, image, description, is_hot, is_new, sales_count, created_at
      FROM dishes 
      WHERE category_id = ? AND status = 1
    `
    const params = [id]

    // 关键词搜索
    if (keyword) {
      sql += ' AND (name LIKE ? OR description LIKE ?)'
      params.push(`%${keyword}%`, `%${keyword}%`)
    }

    // 排序
    const validSorts = ['sort_order', 'price', 'sales_count', 'created_at']
    const sortField = validSorts.includes(sort) ? sort : 'sort_order'
    sql += ` ORDER BY ${sortField} DESC`

    // 分页
    const offset = (page - 1) * limit
    sql += ' LIMIT ? OFFSET ?'
    params.push(parseInt(limit), parseInt(offset))

    const dishes = await query(sql, params)

    // 获取总数
    let countSql = 'SELECT COUNT(*) as total FROM dishes WHERE category_id = ? AND status = 1'
    const countParams = [id]

    if (keyword) {
      countSql += ' AND (name LIKE ? OR description LIKE ?)'
      countParams.push(`%${keyword}%`, `%${keyword}%`)
    }

    const [{ total }] = await query(countSql, countParams)

    res.json({
      code: 0,
      message: '获取成功',
      data: {
        list: dishes,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    console.error('获取分类菜品错误:', error)
    res.status(500).json({
      code: 500,
      message: '获取分类菜品失败'
    })
  }
})

module.exports = router
