// pages/profile/profile.js
const app = getApp()
const { userApi } = require('../../utils/api')

Page({
  data: {
    // 用户信息
    hasLogin: false,
    userInfo: {},
    isAdmin: false,
    
    // 订单统计
    orderStats: {
      total: 0,
      pending: 0,
      preparing: 0,
      delivering: 0
    },
    
    // 优惠券数量
    couponCount: 0
  },

  onLoad() {
    console.log('个人中心页面加载')
  },

  onShow() {
    console.log('个人中心页面显示')
    this.loadUserInfo()
    this.loadUserStats()
  },

  onPullDownRefresh() {
    console.log('下拉刷新')
    this.loadUserInfo()
    this.loadUserStats()
    wx.stopPullDownRefresh()
  },

  // 加载用户信息
  loadUserInfo() {
    const hasLogin = app.globalData.hasLogin
    const userInfo = app.globalData.userInfo || {}
    
    // 判断是否为管理员（这里简单判断，实际应该从后端获取）
    const isAdmin = userInfo.role === 'admin' || userInfo.phone === '13800138000'
    
    this.setData({
      hasLogin,
      userInfo,
      isAdmin
    })
  },

  // 加载用户统计数据
  async loadUserStats() {
    if (!this.data.hasLogin) {
      return
    }

    try {
      // 模拟订单统计数据
      const orderStats = {
        total: 12,
        pending: 1,
        preparing: 2,
        delivering: 1
      }
      
      // 模拟优惠券数量
      const couponCount = 3
      
      this.setData({
        orderStats,
        couponCount
      })
    } catch (error) {
      console.error('加载用户统计失败:', error)
    }
  },

  // 用户登录
  onLogin() {
    console.log('用户登录')
    
    // 获取用户信息
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        console.log('获取用户信息成功:', res.userInfo)
        
        // 模拟登录成功
        const userInfo = {
          ...res.userInfo,
          phone: '138****8888',
          id: 1
        }
        
        app.login(userInfo)
        this.loadUserInfo()
        this.loadUserStats()
        
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })
      },
      fail: (error) => {
        console.error('获取用户信息失败:', error)
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        })
      }
    })
  },

  // 退出登录
  onLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          app.logout()
          this.loadUserInfo()
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  },

  // 订单点击
  onOrderTap(e) {
    if (!this.data.hasLogin) {
      this.showLoginTip()
      return
    }

    const status = e.currentTarget.dataset.status || 'all'
    console.log('查看订单:', status)
    
    wx.navigateTo({
      url: `/pages/order-list/order-list?status=${status}`
    })
  },

  // 地址管理
  onAddressTap() {
    if (!this.data.hasLogin) {
      this.showLoginTip()
      return
    }

    console.log('地址管理')
    wx.navigateTo({
      url: '/pages/address/address'
    })
  },

  // 优惠券
  onCouponTap() {
    if (!this.data.hasLogin) {
      this.showLoginTip()
      return
    }

    console.log('我的优惠券')
    wx.navigateTo({
      url: '/pages/coupon/coupon'
    })
  },

  // 我的收藏
  onFavoriteTap() {
    if (!this.data.hasLogin) {
      this.showLoginTip()
      return
    }

    console.log('我的收藏')
    wx.navigateTo({
      url: '/pages/favorite/favorite'
    })
  },

  // 浏览历史
  onHistoryTap() {
    console.log('浏览历史')
    wx.navigateTo({
      url: '/pages/history/history'
    })
  },

  // 客服中心
  onServiceTap() {
    console.log('客服中心')
    
    wx.showModal({
      title: '客服中心',
      content: '客服电话：400-123-4567\n服务时间：9:00-22:00',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 设置
  onSettingTap() {
    console.log('设置')
    wx.navigateTo({
      url: '/pages/setting/setting'
    })
  },

  // 关于我们
  onAboutTap() {
    console.log('关于我们')
    
    wx.showModal({
      title: '关于我们',
      content: '美食点餐小程序\n版本：v1.0.0\n\n为您提供优质的在线点餐服务',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 菜品管理（管理员）
  onDishManageTap() {
    console.log('菜品管理')
    wx.navigateTo({
      url: '/pages/admin/dish-manage/dish-manage'
    })
  },

  // 订单管理（管理员）
  onOrderManageTap() {
    console.log('订单管理')
    wx.navigateTo({
      url: '/pages/admin/order-manage/order-manage'
    })
  },

  // 显示登录提示
  showLoginTip() {
    wx.showModal({
      title: '提示',
      content: '请先登录后再使用此功能',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          this.onLogin()
        }
      }
    })
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '美食点餐 - 个人中心',
      path: '/pages/profile/profile',
      imageUrl: '/images/share-profile.png'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '美食点餐 - 优质在线点餐服务',
      imageUrl: '/images/share-timeline.png'
    }
  }
})
