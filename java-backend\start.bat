@echo off
chcp 65001 >nul
echo ========================================
echo 🍽️  在线点餐系统Java后端启动脚本
echo ========================================
echo.

echo 📋 检查环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Java环境，请先安装JDK 8或更高版本
    pause
    exit /b 1
)

mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Maven，请先安装Maven
    pause
    exit /b 1
)

echo ✅ Java和Maven环境检查通过

echo.
echo 📦 编译项目...
call mvn clean compile -q
if %errorlevel% neq 0 (
    echo ❌ 项目编译失败
    pause
    exit /b 1
)

echo ✅ 项目编译成功

echo.
echo 🚀 启动应用...
echo 📝 提示: 请确保MySQL数据库已启动并配置正确
echo 🌐 应用将在 http://localhost:8080/api 启动
echo 📖 图片管理API: http://localhost:8080/api/images
echo.
echo 按 Ctrl+C 停止应用
echo ========================================

call mvn spring-boot:run

echo.
echo 应用已停止
pause
