/* pages/search/search.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 40rpx;
}

/* 搜索栏 */
.search-bar {
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
}

.search-input {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid rgba(255, 107, 53, 0.2);
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.search-input:focus-within {
  border-color: #ff6b35;
  box-shadow: 0 6rpx 30rpx rgba(255, 107, 53, 0.2);
}

.search-icon {
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #ff6b35;
}

.input {
  flex: 1;
  font-size: 28rpx;
  color: #2f3542;
}

.clear-btn {
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  transition: all 0.3s ease;
}

.clear-btn:active {
  background: rgba(0, 0, 0, 0.2);
  transform: scale(0.9);
}

.clear-icon {
  font-size: 24rpx;
  color: #666;
}

/* 搜索结果 */
.search-results {
  margin: 20rpx;
}

.results-header {
  padding: 20rpx 0;
}

.results-count {
  font-size: 28rpx;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.result-item {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.result-item:active {
  transform: scale(0.98);
}

.result-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 16rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.result-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.result-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #2f3542;
  flex: 1;
}

.result-tags {
  display: flex;
  gap: 10rpx;
}

.tag {
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  color: white;
  font-weight: bold;
}

.tag.hot {
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
}

.tag.new {
  background: linear-gradient(135deg, #2ed573 0%, #17c0eb 100%);
}

.result-desc {
  font-size: 24rpx;
  color: #57606f;
  line-height: 1.4;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.result-meta {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 15rpx;
}

.category-name {
  font-size: 22rpx;
  color: #ff6b35;
  background: rgba(255, 107, 53, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
}

.sales-count {
  font-size: 22rpx;
  color: #999;
}

.result-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.result-price {
  font-size: 36rpx;
  color: #ff6b35;
  font-weight: bold;
}

.add-btn {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 53, 0.4);
  transition: all 0.3s ease;
}

.add-btn:active {
  transform: scale(0.9);
}

.add-icon {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
}

/* 无结果状态 */
.no-results {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.no-results-content {
  text-align: center;
  color: white;
}

.no-results-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.8;
}

.no-results-text {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.no-results-tip {
  font-size: 24rpx;
  opacity: 0.8;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

/* 搜索建议 */
.search-suggestions {
  margin: 20rpx;
}

.suggestion-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #2f3542;
}

.clear-history {
  font-size: 24rpx;
  color: #ff6b35;
  padding: 8rpx 16rpx;
  border: 1rpx solid #ff6b35;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.clear-history:active {
  background: #ff6b35;
  color: white;
}

.history-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.history-item {
  background: rgba(255, 107, 53, 0.1);
  color: #ff6b35;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  border: 1rpx solid rgba(255, 107, 53, 0.2);
  transition: all 0.3s ease;
}

.history-item:active {
  background: #ff6b35;
  color: white;
  transform: scale(0.95);
}

.hot-keywords {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.hot-keyword {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
  transition: all 0.3s ease;
}

.hot-keyword:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3);
}

/* 状态提示 */
.loading, .load-more, .no-more {
  text-align: center;
  padding: 40rpx;
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}
