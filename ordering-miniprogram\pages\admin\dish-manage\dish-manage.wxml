<!--pages/admin/dish-manage/dish-manage.wxml-->
<view class="container">
  <!-- 顶部操作栏 -->
  <view class="toolbar">
    <view class="search-box">
      <icon class="search-icon" type="search" size="16" color="#999"></icon>
      <input 
        class="search-input" 
        placeholder="搜索菜品名称"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
      />
    </view>
    <button class="add-btn" bindtap="onAddDish">
      <icon type="plus" size="16" color="#fff"></icon>
      <text>添加菜品</text>
    </button>
  </view>

  <!-- 分类筛选 -->
  <scroll-view class="category-filter" scroll-x="{{true}}" show-scrollbar="{{false}}">
    <view class="filter-list">
      <view 
        class="filter-item {{selectedCategory === 0 ? 'active' : ''}}"
        bindtap="onCategoryFilter"
        data-category="{{0}}"
      >
        全部
      </view>
      <view 
        class="filter-item {{selectedCategory === item.id ? 'active' : ''}}"
        wx:for="{{categories}}" 
        wx:key="id"
        bindtap="onCategoryFilter"
        data-category="{{item.id}}"
      >
        {{item.name}}
      </view>
    </view>
  </scroll-view>

  <!-- 菜品列表 -->
  <view class="dish-list">
    <view 
      class="dish-card" 
      wx:for="{{dishes}}" 
      wx:key="id"
    >
      <!-- 菜品图片 -->
      <view class="dish-image-section">
        <image 
          class="dish-image" 
          src="{{item.image || '/images/default-dish.png'}}" 
          mode="aspectFill"
          bindtap="onPreviewImage"
          data-url="{{item.image}}"
        />
        <view class="image-actions">
          <view class="action-btn" bindtap="onUploadImage" data-dish="{{item}}">
            <icon type="camera" size="14" color="#fff"></icon>
          </view>
        </view>
        
        <!-- 状态标签 -->
        <view class="status-tag {{item.status === 1 ? 'active' : 'inactive'}}">
          {{item.status === 1 ? '上架' : '下架'}}
        </view>
        
        <!-- 特色标签 -->
        <view class="feature-tags">
          <text class="tag tag-hot" wx:if="{{item.isHot}}">热</text>
          <text class="tag tag-new" wx:if="{{item.isNew}}">新</text>
          <text class="tag tag-recommend" wx:if="{{item.isRecommended}}">荐</text>
        </view>
      </view>

      <!-- 菜品信息 -->
      <view class="dish-info">
        <view class="dish-header">
          <text class="dish-name">{{item.name}}</text>
          <text class="dish-id">#{{item.id}}</text>
        </view>
        
        <text class="dish-desc">{{item.description}}</text>
        
        <view class="dish-meta">
          <text class="dish-category">{{item.categoryName}}</text>
          <text class="dish-sales">月销 {{item.salesCount}}</text>
          <text class="dish-rating">★ {{item.rating}}</text>
        </view>
        
        <view class="dish-price-section">
          <text class="dish-price">
            <text class="price-symbol">¥</text>{{item.price}}
          </text>
          <text class="original-price" wx:if="{{item.originalPrice && item.originalPrice > item.price}}">
            ¥{{item.originalPrice}}
          </text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="dish-actions">
        <button 
          class="action-button edit-btn" 
          bindtap="onEditDish" 
          data-dish="{{item}}"
        >
          编辑
        </button>
        <button 
          class="action-button status-btn {{item.status === 1 ? 'off' : 'on'}}" 
          bindtap="onToggleStatus" 
          data-dish="{{item}}"
        >
          {{item.status === 1 ? '下架' : '上架'}}
        </button>
        <button 
          class="action-button delete-btn" 
          bindtap="onDeleteDish" 
          data-dish="{{item}}"
        >
          删除
        </button>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}">
    <text class="load-text">{{loadingMore ? '加载中...' : '上拉加载更多'}}</text>
  </view>

  <!-- 没有更多 -->
  <view class="no-more" wx:if="{{!hasMore && dishes.length > 0}}">
    <text class="no-more-text">没有更多菜品了</text>
  </view>

  <!-- 空状态 -->
  <view class="empty" wx:if="{{!loading && dishes.length === 0}}">
    <image class="empty-icon" src="/images/empty-dish.png" />
    <text class="empty-text">暂无菜品</text>
    <text class="empty-desc">点击右上角添加菜品</text>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area"></view>
</view>

<!-- 加载状态 -->
<view class="loading-overlay" wx:if="{{loading}}">
  <view class="loading-content">
    <text>加载中...</text>
  </view>
</view>
