const express = require('express')
const { query } = require('../config/database')
const router = express.Router()

// 获取菜品列表
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      category_id,
      is_hot,
      is_new,
      sort = 'sort_order'
    } = req.query

    let sql = `
      SELECT d.*, c.name as category_name
      FROM dishes d
      LEFT JOIN categories c ON d.category_id = c.id
      WHERE d.status = 1
    `
    const params = []

    // 分类筛选
    if (category_id) {
      sql += ' AND d.category_id = ?'
      params.push(category_id)
    }

    // 热门筛选
    if (is_hot) {
      sql += ' AND d.is_hot = 1'
    }

    // 新品筛选
    if (is_new) {
      sql += ' AND d.is_new = 1'
    }

    // 排序
    const validSorts = ['sort_order', 'sales_count', 'price', 'created_at']
    const sortField = validSorts.includes(sort) ? sort : 'sort_order'
    sql += ` ORDER BY d.${sortField} DESC`

    // 分页
    const offset = (page - 1) * limit
    sql += ' LIMIT ? OFFSET ?'
    params.push(parseInt(limit), parseInt(offset))

    const dishes = await query(sql, params)

    // 获取总数
    let countSql = 'SELECT COUNT(*) as total FROM dishes d WHERE d.status = 1'
    const countParams = []

    if (category_id) {
      countSql += ' AND d.category_id = ?'
      countParams.push(category_id)
    }

    if (is_hot) {
      countSql += ' AND d.is_hot = 1'
    }

    if (is_new) {
      countSql += ' AND d.is_new = 1'
    }

    const [{ total }] = await query(countSql, countParams)

    res.json({
      code: 0,
      message: '获取成功',
      data: {
        list: dishes,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    console.error('获取菜品列表错误:', error)
    res.status(500).json({
      code: 500,
      message: '获取菜品列表失败'
    })
  }
})

// 获取热门菜品
router.get('/hot', async (req, res) => {
  try {
    const { limit = 10 } = req.query

    const dishes = await query(`
      SELECT d.*, c.name as category_name
      FROM dishes d
      LEFT JOIN categories c ON d.category_id = c.id
      WHERE d.status = 1 AND d.is_hot = 1
      ORDER BY d.sales_count DESC, d.sort_order DESC
      LIMIT ?
    `, [parseInt(limit)])

    res.json({
      code: 0,
      message: '获取成功',
      data: dishes
    })
  } catch (error) {
    console.error('获取热门菜品错误:', error)
    res.status(500).json({
      code: 500,
      message: '获取热门菜品失败'
    })
  }
})

// 获取新品推荐
router.get('/new', async (req, res) => {
  try {
    const { limit = 10 } = req.query

    const dishes = await query(`
      SELECT d.*, c.name as category_name
      FROM dishes d
      LEFT JOIN categories c ON d.category_id = c.id
      WHERE d.status = 1 AND d.is_new = 1
      ORDER BY d.created_at DESC, d.sort_order DESC
      LIMIT ?
    `, [parseInt(limit)])

    res.json({
      code: 0,
      message: '获取成功',
      data: dishes
    })
  } catch (error) {
    console.error('获取新品推荐错误:', error)
    res.status(500).json({
      code: 500,
      message: '获取新品推荐失败'
    })
  }
})

// 获取菜品详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params

    const dishes = await query(`
      SELECT d.*, c.name as category_name
      FROM dishes d
      LEFT JOIN categories c ON d.category_id = c.id
      WHERE d.id = ? AND d.status = 1
    `, [id])

    if (dishes.length === 0) {
      return res.status(404).json({
        code: 404,
        message: '菜品不存在'
      })
    }

    res.json({
      code: 0,
      message: '获取成功',
      data: dishes[0]
    })
  } catch (error) {
    console.error('获取菜品详情错误:', error)
    res.status(500).json({
      code: 500,
      message: '获取菜品详情失败'
    })
  }
})

// 搜索菜品
router.get('/search/:keyword', async (req, res) => {
  try {
    const { keyword } = req.params
    const {
      page = 1,
      limit = 20,
      category_id,
      sort = 'sales_count'
    } = req.query

    if (!keyword || keyword.trim().length === 0) {
      return res.status(400).json({
        code: 400,
        message: '搜索关键词不能为空'
      })
    }

    let sql = `
      SELECT d.*, c.name as category_name
      FROM dishes d
      LEFT JOIN categories c ON d.category_id = c.id
      WHERE d.status = 1 AND (
        d.name LIKE ? OR 
        d.description LIKE ? OR
        c.name LIKE ?
      )
    `
    const searchTerm = `%${keyword.trim()}%`
    const params = [searchTerm, searchTerm, searchTerm]

    // 分类筛选
    if (category_id) {
      sql += ' AND d.category_id = ?'
      params.push(category_id)
    }

    // 排序
    const validSorts = ['sales_count', 'price', 'created_at']
    const sortField = validSorts.includes(sort) ? sort : 'sales_count'
    sql += ` ORDER BY d.${sortField} DESC`

    // 分页
    const offset = (page - 1) * limit
    sql += ' LIMIT ? OFFSET ?'
    params.push(parseInt(limit), parseInt(offset))

    const dishes = await query(sql, params)

    // 获取总数
    let countSql = `
      SELECT COUNT(*) as total
      FROM dishes d
      LEFT JOIN categories c ON d.category_id = c.id
      WHERE d.status = 1 AND (
        d.name LIKE ? OR 
        d.description LIKE ? OR
        c.name LIKE ?
      )
    `
    const countParams = [searchTerm, searchTerm, searchTerm]

    if (category_id) {
      countSql += ' AND d.category_id = ?'
      countParams.push(category_id)
    }

    const [{ total }] = await query(countSql, countParams)

    res.json({
      code: 0,
      message: '搜索成功',
      data: {
        list: dishes,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    console.error('搜索菜品错误:', error)
    res.status(500).json({
      code: 500,
      message: '搜索失败'
    })
  }
})

module.exports = router
