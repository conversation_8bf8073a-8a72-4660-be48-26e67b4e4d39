#!/bin/bash

echo "========================================"
echo "微信小程序在线点餐系统启动脚本"
echo "========================================"

echo ""
echo "1. 检查Node.js环境..."
if ! command -v node &> /dev/null; then
    echo "错误：未检测到Node.js，请先安装Node.js"
    exit 1
fi
echo "Node.js环境正常: $(node --version)"

echo ""
echo "2. 进入后端目录..."
cd backend

echo ""
echo "3. 检查依赖包..."
if [ ! -d "node_modules" ]; then
    echo "正在安装依赖包..."
    npm install
    if [ $? -ne 0 ]; then
        echo "错误：依赖包安装失败"
        exit 1
    fi
else
    echo "依赖包已存在"
fi

echo ""
echo "4. 初始化数据库..."
node scripts/init-db.js
if [ $? -ne 0 ]; then
    echo "警告：数据库初始化失败，请检查数据库配置"
fi

echo ""
echo "5. 启动后端服务..."
echo "服务将在 http://localhost:3000 启动"
echo "按 Ctrl+C 可停止服务"
echo ""
npm start
