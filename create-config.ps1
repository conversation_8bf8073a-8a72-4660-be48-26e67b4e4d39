# PowerShell script to create UTF-8 encoded project.config.json
Write-Host "正在创建 UTF-8 编码的 project.config.json 文件..." -ForegroundColor Green

$configContent = @'
{
  "description": "WeChat Mini Program",
  "setting": {
    "urlCheck": false,
    "es6": true,
    "enhance": true,
    "postcss": true,
    "minified": true,
    "newFeature": false,
    "coverView": true,
    "nodeModules": false,
    "autoAudits": false,
    "showShadowRootInWxmlPanel": true,
    "scopeDataCheck": false,
    "checkInvalidKey": true,
    "checkSiteMap": true,
    "uploadWithSourceMap": true,
    "compileHotReLoad": false,
    "useMultiFrameRuntime": true,
    "useApiHook": true,
    "useApiHostProcess": true,
    "minifyWXSS": true,
    "minifyWXML": true
  },
  "compileType": "miniprogram",
  "libVersion": "3.8.5",
  "appid": "wx1234567890abcdef",
  "projectname": "ordering-system"
}
'@

$filePath = "miniprogram/project.config.json"

try {
    # 确保目录存在
    $directory = Split-Path $filePath -Parent
    if (!(Test-Path $directory)) {
        New-Item -ItemType Directory -Path $directory -Force
    }
    
    # 写入UTF-8编码文件（无BOM）
    $utf8NoBom = New-Object System.Text.UTF8Encoding $false
    [System.IO.File]::WriteAllText($filePath, $configContent, $utf8NoBom)
    
    Write-Host "✅ 文件创建成功！" -ForegroundColor Green
    Write-Host "📁 文件位置: $filePath" -ForegroundColor Yellow
    Write-Host "🔧 编码格式: UTF-8 (无BOM)" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "请使用微信开发者工具重新打开项目。" -ForegroundColor Cyan
    
} catch {
    Write-Host "❌ 创建文件失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "按任意键继续..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
