# 🚀 微信小程序在线点餐系统 - 快速启动和调试指南

## 📋 系统要求

### 开发环境
- **Node.js**: >= 14.0.0
- **MySQL**: >= 5.7
- **微信开发者工具**: 最新版本

### 推荐配置
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **内存**: >= 8GB
- **存储**: >= 2GB 可用空间

## 🎯 快速启动步骤

### 1. 环境准备
```bash
# 检查 Node.js 版本
node --version

# 检查 npm 版本
npm --version

# 检查 MySQL 服务状态
# Windows: 服务管理器中查看 MySQL 服务
# macOS/Linux: sudo systemctl status mysql
```

### 2. 一键启动（推荐）
```bash
# Windows 用户
双击运行 start.bat

# Linux/Mac 用户
chmod +x start.sh
./start.sh
```

### 3. 手动启动
```bash
# 1. 启动后端服务
cd backend
npm install
npm start

# 2. 配置小程序
# 打开微信开发者工具
# 导入项目，选择 miniprogram 目录
# 配置 AppID（可使用测试号）
```

## 🔧 配置说明

### 后端配置 (backend/.env)
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=ordering_system

# JWT配置
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=7d

# 微信小程序配置
WECHAT_APP_ID=your-app-id
WECHAT_APP_SECRET=your-app-secret
```

### 小程序配置 (miniprogram/app.js)
```javascript
globalData: {
  baseUrl: 'http://localhost:3000/api'
}
```

## 🐛 调试功能使用

### 1. 开启调试模式
1. 打开小程序，进入"个人中心"页面
2. 连续点击头像 5 次
3. 看到"调试模式已开启"提示
4. 菜单中会出现"系统调试"选项

### 2. 调试页面功能
- **系统信息**: 查看设备和系统信息
- **API状态**: 检查后端服务连接状态
- **功能测试**: 测试各个功能模块
- **调试日志**: 查看实时调试信息
- **导出信息**: 复制调试信息到剪贴板

### 3. 常用调试命令
```javascript
// 在小程序控制台中执行

// 查看应用数据
console.log(getApp().globalData)

// 查看购物车数据
console.log(getApp().getCartData())

// 查看用户信息
console.log(getApp().getUserInfo())

// 清空购物车
getApp().clearCart()

// 清空用户信息
getApp().clearUserInfo()
```

## 🔍 常见问题排查

### 1. 后端服务无法启动
**问题**: `Error: connect ECONNREFUSED`
**解决方案**:
```bash
# 检查 MySQL 服务是否启动
# Windows
net start mysql

# macOS
brew services start mysql

# Linux
sudo systemctl start mysql

# 检查端口是否被占用
netstat -an | grep 3000
```

### 2. 数据库连接失败
**问题**: `ER_ACCESS_DENIED_ERROR`
**解决方案**:
```bash
# 检查数据库配置
# 确认用户名、密码、数据库名是否正确
# 确认 MySQL 用户权限

# 重新创建数据库
mysql -u root -p
CREATE DATABASE ordering_system;
```

### 3. 小程序无法请求接口
**问题**: `request:fail`
**解决方案**:
1. 检查后端服务是否启动 (http://localhost:3000)
2. 检查小程序中的 baseUrl 配置
3. 在微信开发者工具中开启"不校验合法域名"

### 4. 图标显示异常
**问题**: TabBar 图标不显示
**解决方案**:
- 当前使用文字图标（Emoji），无需额外配置
- 如需 PNG 图标，参考 `miniprogram/images/icon-guide.md`

### 5. 登录功能异常
**问题**: 验证码发送失败
**解决方案**:
```javascript
// 开发环境使用固定验证码
// 手机号: 13800138000
// 验证码: 123456
```

## 📊 性能监控

### 1. 前端性能
```javascript
// 在小程序控制台中执行
// 查看页面性能
wx.getPerformance().getEntries()

// 查看内存使用
wx.getSystemInfo({
  success: (res) => {
    console.log('内存信息:', res)
  }
})
```

### 2. 后端性能
```bash
# 查看 Node.js 进程
ps aux | grep node

# 查看端口占用
lsof -i :3000

# 查看数据库连接
mysql -u root -p -e "SHOW PROCESSLIST;"
```

## 🧪 测试数据

### 测试账号
- **手机号**: 13800138000
- **验证码**: 123456（开发环境固定）

### 示例数据
系统会自动创建：
- 6个菜品分类
- 25个示例菜品
- 4个轮播图
- 3个测试用户

## 📱 真机调试

### 1. 配置合法域名
在微信公众平台配置：
```
request合法域名: https://your-domain.com
```

### 2. 上传代码
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 在微信公众平台设置为体验版

### 3. 真机测试
1. 使用微信扫码体验版二维码
2. 测试各项功能
3. 查看真机性能表现

## 🔄 版本更新

### 1. 代码更新
```bash
# 拉取最新代码
git pull origin main

# 更新依赖
cd backend
npm install

# 重启服务
npm restart
```

### 2. 数据库更新
```bash
# 备份数据库
mysqldump -u root -p ordering_system > backup.sql

# 运行更新脚本
node scripts/update-db.js
```

## 📞 技术支持

### 问题反馈
如遇到问题，请提供以下信息：
1. 操作系统和版本
2. Node.js 版本
3. 错误信息截图
4. 调试页面导出的信息

### 调试技巧
1. 使用调试页面检查系统状态
2. 查看浏览器/小程序控制台错误信息
3. 检查网络请求响应
4. 使用断点调试代码逻辑

## 🎉 部署上线

### 1. 生产环境配置
```env
# 修改数据库配置为生产环境
DB_HOST=production-host
DB_PASSWORD=strong-password

# 修改 JWT 密钥
JWT_SECRET=production-secret-key

# 配置真实的微信 AppID
WECHAT_APP_ID=real-app-id
WECHAT_APP_SECRET=real-app-secret
```

### 2. 服务器部署
```bash
# 使用 PM2 管理进程
npm install -g pm2
pm2 start app.js --name ordering-system

# 配置 Nginx 反向代理
# 配置 SSL 证书
# 配置域名解析
```

### 3. 小程序发布
1. 在微信开发者工具中提交审核
2. 等待微信审核通过
3. 发布正式版本

祝您使用愉快！🎊
