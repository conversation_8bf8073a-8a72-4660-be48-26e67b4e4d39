package com.ordering.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 文件上传配置类
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "file.upload")
public class FileUploadConfig {

    /**
     * 上传路径
     */
    private String path = "./uploads/";

    /**
     * 访问路径前缀
     */
    private String accessPath = "/uploads/**";

    /**
     * 允许的文件类型
     */
    private List<String> allowedTypes = Arrays.asList("jpg", "jpeg", "png", "gif", "webp");

    /**
     * 最大文件大小（字节）
     */
    private Long maxSize = 10485760L; // 10MB

    /**
     * 压缩配置
     */
    private CompressConfig compress = new CompressConfig();

    @Data
    public static class CompressConfig {
        /**
         * 是否启用压缩
         */
        private Boolean enabled = true;

        /**
         * 压缩质量 (0.1-1.0)
         */
        private Double quality = 0.8;

        /**
         * 最大宽度
         */
        private Integer maxWidth = 1920;

        /**
         * 最大高度
         */
        private Integer maxHeight = 1080;

        /**
         * 缩略图配置
         */
        private ThumbnailConfig thumbnail = new ThumbnailConfig();

        @Data
        public static class ThumbnailConfig {
            /**
             * 缩略图宽度
             */
            private Integer width = 300;

            /**
             * 缩略图高度
             */
            private Integer height = 300;
        }
    }
}
