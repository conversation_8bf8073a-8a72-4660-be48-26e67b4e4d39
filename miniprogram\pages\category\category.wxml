<!--pages/category/category.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar" bindtap="onSearchTap">
      <text class="search-icon">🔍</text>
      <text class="search-placeholder">搜索菜品</text>
    </view>
  </view>

  <!-- 分类和菜品内容 -->
  <view class="content-section">
    <!-- 左侧分类列表 -->
    <scroll-view class="category-sidebar" scroll-y="{{true}}">
      <view 
        class="category-item {{selectedCategoryId === category.id ? 'active' : ''}}"
        wx:for="{{categories}}"
        wx:key="id"
        wx:for-item="category"
        bindtap="onCategoryTap"
        data-id="{{category.id}}"
      >
        <image 
          class="category-icon" 
          src="{{category.icon || '/images/category-default.png'}}" 
          mode="aspectFill"
        />
        <text class="category-name">{{category.name}}</text>
        <view class="category-indicator" wx:if="{{selectedCategoryId === category.id}}"></view>
      </view>
    </scroll-view>

    <!-- 右侧菜品列表 -->
    <scroll-view class="dishes-content" scroll-y="{{true}}">
      <!-- 排序选项 -->
      <view class="sort-section">
        <view class="sort-options">
          <view 
            class="sort-item {{sortType === 'default' ? 'active' : ''}}"
            bindtap="onSortChange"
            data-type="default"
          >
            <text class="sort-text">默认</text>
          </view>
          <view 
            class="sort-item {{sortType === 'sales' ? 'active' : ''}}"
            bindtap="onSortChange"
            data-type="sales"
          >
            <text class="sort-text">销量</text>
          </view>
          <view 
            class="sort-item {{sortType === 'price_asc' ? 'active' : ''}}"
            bindtap="onSortChange"
            data-type="price_asc"
          >
            <text class="sort-text">价格↑</text>
          </view>
          <view 
            class="sort-item {{sortType === 'price_desc' ? 'active' : ''}}"
            bindtap="onSortChange"
            data-type="price_desc"
          >
            <text class="sort-text">价格↓</text>
          </view>
        </view>
      </view>

      <!-- 菜品列表 -->
      <view class="dishes-list">
        <dish-card 
          wx:for="{{dishes}}" 
          wx:key="id"
          dish="{{item}}"
          mode="normal"
          bind:dishTap="onDishTap"
          bind:addToCart="onAddToCart"
          bind:toggleLike="onToggleLike"
        />
      </view>

      <!-- 加载状态 -->
      <view class="loading-section" wx:if="{{loading}}">
        <loading-animation 
          show="{{loading}}"
          type="dots"
          text="加载中..."
        />
      </view>

      <!-- 没有更多数据 -->
      <view class="no-more" wx:if="{{!loading && dishes.length > 0 && !hasMore}}">
        <text class="no-more-text">没有更多菜品了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{!loading && dishes.length === 0}}">
        <image class="empty-image" src="/images/empty-dishes.png" mode="aspectFit"></image>
        <text class="empty-text">暂无菜品</text>
        <text class="empty-tip">换个分类试试吧</text>
      </view>
    </scroll-view>
  </view>

  <!-- 悬浮购物车 -->
  <floating-cart 
    cart-count="{{cartCount}}"
    total-price="{{totalPrice}}"
    show="{{cartCount > 0}}"
    bind:cartTap="onFloatingCartTap"
    bind:checkoutTap="onFloatingCheckoutTap"
  />
</view>
