<!--pages/debug/debug.wxml-->
<view class="container">
  <view class="debug-header">
    <text class="debug-title">🔧 系统调试</text>
    <text class="debug-subtitle">检查系统运行状态</text>
  </view>

  <!-- 系统信息 -->
  <view class="debug-section">
    <view class="section-title">
      <text class="title-icon">📱</text>
      <text class="title-text">系统信息</text>
    </view>
    <view class="info-grid">
      <view class="info-item">
        <text class="info-label">设备型号</text>
        <text class="info-value">{{systemInfo.model}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">系统版本</text>
        <text class="info-value">{{systemInfo.system}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">微信版本</text>
        <text class="info-value">{{systemInfo.version}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">网络状态</text>
        <text class="info-value {{networkStatus === '正常' ? 'success' : 'error'}}">{{networkStatus}}</text>
      </view>
    </view>
  </view>

  <!-- API状态 -->
  <view class="debug-section">
    <view class="section-title">
      <text class="title-icon">🌐</text>
      <text class="title-text">API状态</text>
    </view>
    <view class="status-grid">
      <view class="status-item">
        <text class="status-label">后端服务</text>
        <view class="status-indicator {{apiStatus.backend}}">
          <text class="status-text">{{apiStatus.backend === 'success' ? '正常' : apiStatus.backend === 'error' ? '异常' : '检测中'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 测试结果 -->
  <view class="debug-section" wx:if="{{testResults.length > 0}}">
    <view class="section-title">
      <text class="title-icon">🧪</text>
      <text class="title-text">API测试结果</text>
    </view>
    <view class="test-list">
      <view 
        class="test-item" 
        wx:for="{{testResults}}" 
        wx:key="name"
        bindtap="onViewDetail"
        data-type="{{item.name}}"
        data-data="{{item}}"
      >
        <view class="test-header">
          <text class="test-name">{{item.name}}</text>
          <view class="test-status {{item.status}}">
            <text class="status-icon">{{item.status === 'success' ? '✅' : '❌'}}</text>
          </view>
        </view>
        <view class="test-meta">
          <text class="test-duration" wx:if="{{item.duration}}">{{item.duration}}</text>
          <text class="test-error" wx:if="{{item.error}}">{{item.error}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 应用数据 -->
  <view class="debug-section">
    <view class="section-title">
      <text class="title-icon">📊</text>
      <text class="title-text">应用数据</text>
    </view>
    <view class="data-grid">
      <view class="data-item">
        <text class="data-label">购物车商品</text>
        <text class="data-value">{{cartData.cart ? cartData.cart.length : 0}} 件</text>
      </view>
      <view class="data-item">
        <text class="data-label">购物车总价</text>
        <text class="data-value">¥{{cartData.totalPrice || 0}}</text>
      </view>
      <view class="data-item">
        <text class="data-label">用户状态</text>
        <text class="data-value {{userInfo.phone ? 'success' : 'error'}}">
          {{userInfo.phone ? '已登录' : '未登录'}}
        </text>
      </view>
    </view>
  </view>

  <!-- 功能测试 -->
  <view class="debug-section">
    <view class="section-title">
      <text class="title-icon">🔨</text>
      <text class="title-text">功能测试</text>
    </view>
    <view class="test-buttons">
      <button class="test-btn" bindtap="testCartFunction">测试购物车</button>
      <button class="test-btn" bindtap="testLocalStorage">测试本地存储</button>
      <button class="test-btn" bindtap="testNetworkRequest">测试网络请求</button>
      <button class="test-btn danger" bindtap="clearCart">清空购物车</button>
    </view>
  </view>

  <!-- 调试日志 -->
  <view class="debug-section" wx:if="{{debugLogs.length > 0}}">
    <view class="section-title">
      <text class="title-icon">📝</text>
      <text class="title-text">调试日志</text>
      <button class="clear-btn" bindtap="clearLogs">清空</button>
    </view>
    <view class="log-container">
      <view class="log-item" wx:for="{{debugLogs}}" wx:key="*this">
        <text class="log-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="debug-actions">
    <button class="action-btn primary" bindtap="onRetest">重新测试</button>
    <button class="action-btn secondary" bindtap="exportDebugInfo">导出信息</button>
  </view>
</view>
