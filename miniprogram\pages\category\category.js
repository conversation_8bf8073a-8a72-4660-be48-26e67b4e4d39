// pages/category/category.js
const { get } = require('../../utils/request')
const { showLoading, hideLoading, debounce } = require('../../utils/util')

Page({
  data: {
    categories: [],
    dishes: [],
    selectedCategoryId: null,
    loading: false,
    hasMore: true,
    page: 1,
    searchKeyword: '',
    sortType: 'default', // default, price_asc, price_desc, sales
    cartCount: 0,
    totalPrice: 0
  },

  onLoad() {
    this.loadCategories()
    this.updateCartCount()
  },

  onShow() {
    this.updateCartCount()
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreDishes()
    }
  },

  onPullDownRefresh() {
    this.refreshData()
  },

  // 加载分类列表
  async loadCategories() {
    try {
      showLoading('加载中...')
      const res = await get('/categories')
      
      this.setData({
        categories: res.data
      })
      
      // 默认选择第一个分类
      if (res.data.length > 0) {
        this.selectCategory(res.data[0].id)
      }
      
    } catch (error) {
      console.error('加载分类失败:', error)
      wx.showToast({
        title: '加载分类失败',
        icon: 'none'
      })
    } finally {
      hideLoading()
    }
  },

  // 选择分类
  selectCategory(categoryId) {
    this.setData({
      selectedCategoryId: categoryId,
      dishes: [],
      page: 1,
      hasMore: true
    })
    
    this.loadDishes()
  },

  // 分类点击事件
  onCategoryTap(e) {
    const { id } = e.currentTarget.dataset
    this.selectCategory(id)
  },

  // 加载菜品列表
  async loadDishes() {
    if (this.data.loading) return
    
    try {
      this.setData({ loading: true })
      
      const params = {
        page: this.data.page,
        limit: 20,
        category_id: this.data.selectedCategoryId,
        sort: this.data.sortType
      }
      
      if (this.data.searchKeyword) {
        params.keyword = this.data.searchKeyword
      }
      
      const res = await get('/dishes', params)
      
      const newDishes = this.data.page === 1 ? res.data.list : [...this.data.dishes, ...res.data.list]
      
      this.setData({
        dishes: newDishes,
        hasMore: res.data.pagination.page < res.data.pagination.pages,
        loading: false
      })
      
    } catch (error) {
      console.error('加载菜品失败:', error)
      this.setData({ loading: false })
      wx.showToast({
        title: '加载菜品失败',
        icon: 'none'
      })
    }
  },

  // 加载更多菜品
  async loadMoreDishes() {
    this.setData({
      page: this.data.page + 1
    })
    await this.loadDishes()
  },

  // 刷新数据
  async refreshData() {
    this.setData({
      page: 1,
      hasMore: true,
      dishes: []
    })
    
    await Promise.all([
      this.loadCategories(),
      this.loadDishes()
    ])
    
    wx.stopPullDownRefresh()
  },

  // 搜索输入
  onSearchInput: debounce(function(e) {
    const keyword = e.detail.value
    this.setData({
      searchKeyword: keyword,
      page: 1,
      hasMore: true,
      dishes: []
    })
    
    if (keyword.trim()) {
      this.loadDishes()
    } else {
      this.loadDishes()
    }
  }, 500),

  // 搜索确认
  onSearchConfirm() {
    this.setData({
      page: 1,
      hasMore: true,
      dishes: []
    })
    this.loadDishes()
  },

  // 清除搜索
  onClearSearch() {
    this.setData({
      searchKeyword: '',
      page: 1,
      hasMore: true,
      dishes: []
    })
    this.loadDishes()
  },

  // 排序切换
  onSortChange(e) {
    const { type } = e.currentTarget.dataset
    
    this.setData({
      sortType: type,
      page: 1,
      hasMore: true,
      dishes: []
    })
    
    this.loadDishes()
  },

  // 点击菜品
  onDishTap(e) {
    const { dish } = e.detail
    wx.navigateTo({
      url: `/pages/detail/detail?id=${dish.id}`
    })
  },

  // 添加到购物车
  onAddToCart(e) {
    const { dish } = e.detail
    const app = getApp()
    
    const success = app.addToCart({
      id: dish.id,
      name: dish.name,
      price: dish.price,
      image: dish.image,
      quantity: 1
    })
    
    if (success) {
      wx.showToast({
        title: '已添加到购物车',
        icon: 'success'
      })
      
      this.updateCartCount()
    }
  },

  // 切换点赞
  onToggleLike(e) {
    const { dish, isLiked } = e.detail
    // 这里可以调用API保存用户的点赞状态
    console.log(`${isLiked ? '点赞' : '取消点赞'} 菜品:`, dish.name)
    
    // 可以添加到收藏列表
    if (isLiked) {
      wx.showToast({
        title: '已添加到收藏',
        icon: 'success'
      })
    }
  },

  // 悬浮购物车点击
  onFloatingCartTap() {
    wx.switchTab({
      url: '/pages/cart/cart'
    })
  },

  // 悬浮购物车结算
  onFloatingCheckoutTap() {
    wx.switchTab({
      url: '/pages/cart/cart'
    })
  },

  // 更新购物车数量
  updateCartCount() {
    const app = getApp()
    const cartData = app.getCartData()
    
    this.setData({
      cartCount: cartData.totalCount,
      totalPrice: cartData.totalPrice
    })
    
    if (cartData.totalCount > 0) {
      wx.setTabBarBadge({
        index: 2,
        text: cartData.totalCount.toString()
      })
    } else {
      wx.removeTabBarBadge({
        index: 2
      })
    }
  },

  // 跳转到搜索页面
  onSearchTap() {
    wx.navigateTo({
      url: '/pages/search/search'
    })
  }
})
