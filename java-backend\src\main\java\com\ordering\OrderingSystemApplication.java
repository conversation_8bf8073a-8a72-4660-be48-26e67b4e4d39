package com.ordering;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 在线点餐系统启动类
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@SpringBootApplication
@EnableTransactionManagement
@MapperScan("com.ordering.mapper")
@EnableConfigurationProperties
public class OrderingSystemApplication {

    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("🍽️  在线点餐系统后端服务启动中...");
        System.out.println("========================================");
        
        SpringApplication.run(OrderingSystemApplication.class, args);
        
        System.out.println("========================================");
        System.out.println("✅ 在线点餐系统后端服务启动成功！");
        System.out.println("🌐 访问地址: http://localhost:8080/api");
        System.out.println("📖 API文档: http://localhost:8080/api/doc.html");
        System.out.println("🖼️  图片管理: http://localhost:8080/api/images");
        System.out.println("========================================");
    }
}
