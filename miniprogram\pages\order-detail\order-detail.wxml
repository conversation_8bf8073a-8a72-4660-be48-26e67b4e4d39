<!--pages/order-detail/order-detail.wxml-->
<view class="container" wx:if="{{!loading && order}}">
  <!-- 订单状态 -->
  <view class="status-section">
    <view class="status-icon" style="color: {{statusColorMap[order.status]}}">
      <text wx:if="{{order.status === 'pending'}}">⏳</text>
      <text wx:if="{{order.status === 'confirmed'}}">✅</text>
      <text wx:if="{{order.status === 'preparing'}}">👨‍🍳</text>
      <text wx:if="{{order.status === 'completed'}}">🎉</text>
      <text wx:if="{{order.status === 'cancelled'}}">❌</text>
    </view>
    <view class="status-info">
      <text class="status-text" style="color: {{statusColorMap[order.status]}}">
        {{statusMap[order.status]}}
      </text>
      <text class="status-desc">
        <text wx:if="{{order.status === 'pending'}}">您的订单已提交，等待商家确认</text>
        <text wx:if="{{order.status === 'confirmed'}}">商家已确认订单，正在准备制作</text>
        <text wx:if="{{order.status === 'preparing'}}">您的美食正在精心制作中</text>
        <text wx:if="{{order.status === 'completed'}}">订单已完成，感谢您的光临</text>
        <text wx:if="{{order.status === 'cancelled'}}">订单已取消</text>
      </text>
    </view>
  </view>

  <!-- 订单信息 -->
  <view class="order-info-section">
    <view class="section-title">
      <text class="title-icon">📋</text>
      <text class="title-text">订单信息</text>
      <text class="copy-btn" bindtap="onCopyOrderNo">复制订单号</text>
    </view>
    
    <view class="info-row">
      <text class="info-label">订单号</text>
      <text class="info-value">{{order.order_no}}</text>
    </view>
    
    <view class="info-row">
      <text class="info-label">下单时间</text>
      <text class="info-value">{{formatTime(order.created_at)}}</text>
    </view>
    
    <view class="info-row">
      <text class="info-label">联系人</text>
      <text class="info-value">{{order.contact_name}}</text>
    </view>
    
    <view class="info-row">
      <text class="info-label">联系电话</text>
      <text class="info-value">{{order.contact_phone}}</text>
    </view>
    
    <view class="info-row" wx:if="{{order.delivery_address}}">
      <text class="info-label">配送地址</text>
      <text class="info-value address">{{order.delivery_address}}</text>
    </view>
    
    <view class="info-row" wx:if="{{order.remark}}">
      <text class="info-label">备注</text>
      <text class="info-value">{{order.remark}}</text>
    </view>
  </view>

  <!-- 订单商品 -->
  <view class="order-items-section">
    <view class="section-title">
      <text class="title-icon">🛒</text>
      <text class="title-text">订单商品</text>
    </view>
    
    <view class="order-items">
      <view 
        class="order-item" 
        wx:for="{{order.items}}" 
        wx:key="id"
        bindtap="onDishTap"
        data-item="{{item}}"
      >
        <image 
          class="item-image" 
          src="{{item.dish_image || '/images/dish-default.jpg'}}" 
          mode="aspectFill"
        />
        <view class="item-info">
          <text class="item-name">{{item.dish_name}}</text>
          <view class="item-details">
            <text class="item-price">¥{{item.dish_price}}</text>
            <text class="item-quantity">×{{item.quantity}}</text>
          </view>
        </view>
        <text class="item-subtotal">¥{{item.subtotal.toFixed(2)}}</text>
      </view>
    </view>
  </view>

  <!-- 费用明细 -->
  <view class="cost-section">
    <view class="section-title">
      <text class="title-icon">💰</text>
      <text class="title-text">费用明细</text>
    </view>
    
    <view class="cost-row">
      <text class="cost-label">商品总计</text>
      <text class="cost-value">¥{{order.total_amount}}</text>
    </view>
    
    <view class="cost-row">
      <text class="cost-label">配送费</text>
      <text class="cost-value free">免费</text>
    </view>
    
    <view class="cost-row total">
      <text class="cost-label">实付金额</text>
      <text class="cost-value total-amount">¥{{order.total_amount}}</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <button 
      class="action-btn cancel-btn" 
      wx:if="{{order.status === 'pending'}}"
      bindtap="onCancelOrder"
    >
      取消订单
    </button>
    
    <button 
      class="action-btn reorder-btn"
      wx:if="{{order.status === 'completed' || order.status === 'cancelled'}}"
      bindtap="onReorder"
    >
      再来一单
    </button>
    
    <button class="action-btn service-btn" bindtap="onContactService">
      联系客服
    </button>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-container" wx:if="{{loading}}">
  <view class="loading">
    <text>加载中...</text>
  </view>
</view>

<!-- 错误状态 -->
<view class="error-container" wx:if="{{!loading && !order}}">
  <view class="error">
    <text>订单不存在或已删除</text>
  </view>
</view>
