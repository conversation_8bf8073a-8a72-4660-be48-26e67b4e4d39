// pages/search/search.js
const { get } = require('../../utils/request')
const { showLoading, hideLoading, getImageUrl } = require('../../utils/util')

Page({
  data: {
    keyword: '',
    searchResults: [],
    loading: false,
    hasMore: true,
    page: 1,
    total: 0,
    searchHistory: [],
    hotKeywords: ['川菜', '粤菜', '湘菜', '素食', '甜品', '汤类']
  },

  onLoad(options) {
    const { keyword } = options
    if (keyword) {
      this.setData({ keyword })
      wx.setNavigationBarTitle({
        title: `搜索: ${keyword}`
      })
      this.performSearch(keyword)
    }
    this.loadSearchHistory()
  },

  onShow() {
    this.updateCartCount()
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreResults()
    }
  },

  onPullDownRefresh() {
    this.setData({
      page: 1,
      hasMore: true,
      searchResults: []
    })
    this.performSearch(this.data.keyword)
    wx.stopPullDownRefresh()
  },

  // 执行搜索
  async performSearch(keyword) {
    if (!keyword.trim()) return

    try {
      this.setData({ loading: true })
      showLoading('搜索中...')

      const res = await get(`/dishes/search/${encodeURIComponent(keyword)}`, {
        page: this.data.page,
        limit: 20
      })

      this.setData({
        searchResults: this.data.page === 1 ? res.data.list : [...this.data.searchResults, ...res.data.list],
        total: res.data.pagination.total,
        hasMore: res.data.pagination.page < res.data.pagination.pages,
        loading: false
      })

      // 保存搜索历史
      this.saveSearchHistory(keyword)

    } catch (error) {
      console.error('搜索失败:', error)
      this.setData({ loading: false })
      wx.showToast({
        title: '搜索失败',
        icon: 'none'
      })
    } finally {
      hideLoading()
    }
  },

  // 加载更多结果
  async loadMoreResults() {
    const nextPage = this.data.page + 1
    this.setData({ page: nextPage })
    await this.performSearch(this.data.keyword)
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      keyword: e.detail.value
    })
  },

  // 搜索确认
  onSearchConfirm() {
    const { keyword } = this.data
    if (keyword.trim()) {
      this.setData({
        page: 1,
        hasMore: true,
        searchResults: []
      })
      this.performSearch(keyword.trim())
    }
  },

  // 清除搜索
  onClearSearch() {
    this.setData({
      keyword: '',
      searchResults: [],
      page: 1,
      hasMore: true,
      total: 0
    })
  },

  // 点击热门关键词
  onHotKeywordTap(e) {
    const { keyword } = e.currentTarget.dataset
    this.setData({
      keyword,
      page: 1,
      hasMore: true,
      searchResults: []
    })
    this.performSearch(keyword)
  },

  // 点击历史记录
  onHistoryTap(e) {
    const { keyword } = e.currentTarget.dataset
    this.setData({
      keyword,
      page: 1,
      hasMore: true,
      searchResults: []
    })
    this.performSearch(keyword)
  },

  // 清除搜索历史
  onClearHistory() {
    wx.showModal({
      title: '提示',
      content: '确定要清除搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ searchHistory: [] })
          wx.removeStorageSync('searchHistory')
        }
      }
    })
  },

  // 点击菜品
  onDishTap(e) {
    const { item } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/detail/detail?id=${item.id}`
    })
  },

  // 添加到购物车
  onAddToCart(e) {
    e.stopPropagation()
    
    const { item } = e.currentTarget.dataset
    const app = getApp()
    
    app.addToCart({
      id: item.id,
      name: item.name,
      price: item.price,
      image: item.image,
      quantity: 1
    })
    
    wx.showToast({
      title: '已添加到购物车',
      icon: 'success'
    })
    
    this.updateCartCount()
  },

  // 加载搜索历史
  loadSearchHistory() {
    const history = wx.getStorageSync('searchHistory') || []
    this.setData({ searchHistory: history })
  },

  // 保存搜索历史
  saveSearchHistory(keyword) {
    let history = this.data.searchHistory
    
    // 移除重复项
    history = history.filter(item => item !== keyword)
    
    // 添加到开头
    history.unshift(keyword)
    
    // 限制历史记录数量
    if (history.length > 10) {
      history = history.slice(0, 10)
    }
    
    this.setData({ searchHistory: history })
    wx.setStorageSync('searchHistory', history)
  },

  // 更新购物车数量
  updateCartCount() {
    const app = getApp()
    const cartData = app.getCartData()
    const cartCount = cartData.cart.reduce((total, item) => total + item.quantity, 0)
    
    if (cartCount > 0) {
      wx.setTabBarBadge({
        index: 2,
        text: cartCount.toString()
      })
    } else {
      wx.removeTabBarBadge({
        index: 2
      })
    }
  },

  // 获取图片URL
  getImageUrl(path) {
    return getImageUrl(path)
  }
})
