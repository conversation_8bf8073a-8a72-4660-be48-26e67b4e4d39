/* pages/menu/menu.wxss */

.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f8f8;
}

/* 搜索栏 */
.search-bar {
  padding: 20rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-input {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: #f8f8f8;
  border-radius: 50rpx;
}

.search-icon {
  margin-right: 20rpx;
}

.search-placeholder {
  flex: 1;
  color: #999;
  font-size: 28rpx;
}

.search-btn {
  color: #ff6b35;
  font-size: 28rpx;
  font-weight: 500;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧分类栏 */
.category-sidebar {
  width: 200rpx;
  background: #fff;
  border-right: 1rpx solid #f0f0f0;
}

.category-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f8f8f8;
  transition: all 0.3s ease;
}

.category-item.active {
  background: #fff;
  border-right: 4rpx solid #ff6b35;
}

.category-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 8rpx;
  background: #ff6b35;
}

.category-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.category-item.active .category-name {
  color: #ff6b35;
}

.category-count {
  font-size: 20rpx;
  color: #999;
  background: #f0f0f0;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 32rpx;
  text-align: center;
}

.category-item.active .category-count {
  background: rgba(255, 107, 53, 0.1);
  color: #ff6b35;
}

/* 右侧菜品内容 */
.dish-content {
  flex: 1;
  background: #fff;
}

/* 分类标题 */
.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.category-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.dish-count {
  font-size: 24rpx;
  color: #999;
}

/* 菜品列表 */
.dish-list {
  padding: 0 20rpx;
}

.dish-item {
  display: flex;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.dish-item:last-child {
  border-bottom: none;
}

.dish-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.dish-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.dish-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.dish-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  flex: 1;
  margin-right: 20rpx;
}

.dish-tags {
  display: flex;
  flex-wrap: wrap;
}

.tag {
  font-size: 18rpx;
  font-weight: 500;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  margin-left: 8rpx;
  margin-bottom: 4rpx;
}

.tag-hot {
  background: #ff4757;
  color: #fff;
}

.tag-new {
  background: #2ecc71;
  color: #fff;
}

.tag-recommend {
  background: #ffc107;
  color: #fff;
}

.dish-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.dish-meta {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.dish-sales {
  font-size: 22rpx;
  color: #999;
  margin-right: 20rpx;
}

.dish-rating {
  font-size: 22rpx;
  color: #ffc107;
}

.dish-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.price-section {
  display: flex;
  align-items: center;
}

.dish-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff6b35;
  margin-right: 12rpx;
}

.price-symbol {
  font-size: 24rpx;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

/* 数量控制器 */
.quantity-control {
  display: flex;
  align-items: center;
  border: 2rpx solid #ff6b35;
  border-radius: 40rpx;
  overflow: hidden;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  transition: all 0.3s ease;
}

.quantity-btn:active {
  background: #f8f8f8;
}

.quantity-text {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #ff6b35;
  background: #fff;
}

.add-btn {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
}

/* 加载更多 */
.load-more {
  padding: 40rpx;
  text-align: center;
}

.load-text {
  font-size: 28rpx;
  color: #999;
}

.no-more {
  padding: 40rpx;
  text-align: center;
}

.no-more-text {
  font-size: 28rpx;
  color: #ccc;
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #ccc;
}

/* 购物车栏 */
.cart-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.cart-info {
  flex: 1;
  display: flex;
  align-items: center;
}

.cart-icon-wrapper {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.cart-count {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4757;
  color: #fff;
  font-size: 20rpx;
  font-weight: 500;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1;
}

.cart-text {
  display: flex;
  flex-direction: column;
}

.cart-total {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

.delivery-fee {
  font-size: 22rpx;
  color: #999;
  line-height: 1.2;
}

.checkout-btn {
  padding: 24rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

.checkout-btn.active {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: #fff;
}

.checkout-btn.disabled {
  background: #f0f0f0;
  color: #999;
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  font-size: 28rpx;
  color: #999;
}
