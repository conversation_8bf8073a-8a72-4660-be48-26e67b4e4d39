/* pages/profile/profile.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 40rpx;
}

/* 用户信息区域 */
.user-section {
  position: relative;
  margin: 20rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.2);
}

.user-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  backdrop-filter: blur(20rpx);
}

.user-content {
  position: relative;
  z-index: 2;
  padding: 40rpx 30rpx;
}

.user-info, .login-prompt {
  display: flex;
  align-items: center;
}

.user-avatar, .default-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
}

.user-details, .login-info {
  flex: 1;
}

.user-name, .login-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2f3542;
  display: block;
  margin-bottom: 8rpx;
}

.user-phone, .login-subtitle {
  font-size: 26rpx;
  color: #57606f;
  opacity: 0.8;
}

.logout-btn, .login-btn {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  font-size: 26rpx;
  font-weight: 500;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 53, 0.4);
  transition: all 0.3s ease;
}

.logout-btn:active, .login-btn:active {
  transform: scale(0.95);
}

/* 订单统计 */
.order-stats {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  margin: 20rpx;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2f3542;
}

.stats-more {
  font-size: 26rpx;
  color: #ff6b35;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.stats-item {
  text-align: center;
  padding: 20rpx;
  background: rgba(255, 107, 53, 0.05);
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.stats-item:active {
  background: rgba(255, 107, 53, 0.1);
  transform: scale(0.95);
}

.stats-count {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b35;
  display: block;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #57606f;
}

/* 功能菜单 */
.menu-section {
  margin: 20rpx;
}

.menu-group {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: rgba(255, 107, 53, 0.05);
}

.menu-icon {
  font-size: 36rpx;
  margin-right: 25rpx;
  width: 50rpx;
  text-align: center;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #2f3542;
  font-weight: 500;
}

.menu-arrow {
  font-size: 28rpx;
  color: #999;
  font-weight: bold;
}
