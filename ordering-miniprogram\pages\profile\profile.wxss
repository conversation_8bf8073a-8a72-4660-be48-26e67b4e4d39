/* pages/profile/profile.wxss */

.container {
  min-height: 100vh;
  background: #f8f8f8;
}

/* 用户信息区域 */
.user-section {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  padding: 60rpx 30rpx 40rpx;
  color: #fff;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.user-phone {
  display: block;
  font-size: 26rpx;
  opacity: 0.8;
}

.user-level {
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.level-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* 登录区域 */
.login-section {
  display: flex;
  align-items: center;
}

.login-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
  opacity: 0.8;
}

.login-info {
  flex: 1;
}

.login-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.login-desc {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

.login-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  font-weight: 500;
}

.login-btn:active {
  background: rgba(255, 255, 255, 0.1);
}

/* 订单统计 */
.order-stats {
  display: flex;
  background: #fff;
  margin: -20rpx 30rpx 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.stats-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 20rpx;
  border-right: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.stats-item:last-child {
  border-right: none;
}

.stats-item:active {
  background: #f8f8f8;
}

.stats-number {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff6b35;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 功能菜单 */
.menu-section {
  padding: 0 30rpx;
}

.menu-group {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: #f8f8f8;
}

.menu-item.logout:active {
  background: rgba(255, 71, 87, 0.1);
}

.menu-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.menu-icon {
  margin-right: 20rpx;
}

.menu-text {
  font-size: 28rpx;
  color: #333;
}

.menu-text.logout-text {
  color: #ff4757;
}

.menu-right {
  display: flex;
  align-items: center;
}

.menu-badge {
  background: #ff4757;
  color: #fff;
  font-size: 20rpx;
  font-weight: 500;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 32rpx;
  text-align: center;
  margin-right: 12rpx;
}

.menu-arrow {
  opacity: 0.5;
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 40rpx;
}

.version-text {
  font-size: 24rpx;
  color: #ccc;
}

/* 安全区域 */
.safe-area {
  height: env(safe-area-inset-bottom);
}
