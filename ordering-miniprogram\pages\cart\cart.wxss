/* pages/cart/cart.wxss */

.container {
  min-height: 100vh;
  background: #f8f8f8;
  padding-bottom: 120rpx;
}

/* 购物车内容 */
.cart-content {
  padding: 20rpx;
}

/* 商家信息 */
.merchant-info {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.merchant-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.merchant-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 20rpx;
}

.merchant-status {
  background: #2ecc71;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.merchant-desc {
  font-size: 24rpx;
  color: #999;
}

/* 购物车列表 */
.cart-list {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.cart-item {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.cart-item:last-child {
  border-bottom: none;
}

.item-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.item-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.item-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.item-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.item-price {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff6b35;
}

.price-symbol {
  font-size: 24rpx;
}

/* 数量控制器 */
.quantity-control {
  display: flex;
  align-items: center;
  border: 2rpx solid #ff6b35;
  border-radius: 40rpx;
  overflow: hidden;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  transition: all 0.3s ease;
}

.quantity-btn:active {
  background: #f8f8f8;
}

.quantity-text {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #ff6b35;
  background: #fff;
}

/* 优惠券区域 */
.coupon-section {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.coupon-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
}

.coupon-left {
  display: flex;
  align-items: center;
}

.coupon-text {
  font-size: 28rpx;
  color: #333;
  margin-left: 16rpx;
}

.coupon-right {
  display: flex;
  align-items: center;
}

.coupon-desc {
  font-size: 24rpx;
  color: #999;
  margin-right: 12rpx;
}

/* 配送信息 */
.delivery-section {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.delivery-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.delivery-item:last-child {
  border-bottom: none;
}

.delivery-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.delivery-info {
  margin-left: 16rpx;
  flex: 1;
}

.delivery-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.delivery-desc {
  display: block;
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

/* 备注区域 */
.remark-section {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.remark-header {
  padding: 30rpx 30rpx 0;
}

.remark-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.remark-input {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx 30rpx 30rpx;
  font-size: 26rpx;
  color: #333;
  line-height: 1.6;
  box-sizing: border-box;
}

/* 费用明细 */
.cost-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.cost-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.cost-item:last-child {
  margin-bottom: 0;
}

.cost-label {
  font-size: 26rpx;
  color: #666;
}

.cost-value {
  font-size: 26rpx;
  color: #333;
}

.cost-value.discount {
  color: #ff6b35;
}

.cost-item.total .cost-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.cost-item.total .cost-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff6b35;
}

.cost-divider {
  height: 1rpx;
  background: #f0f0f0;
  margin: 20rpx 0;
}

/* 空购物车 */
.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #ccc;
  margin-bottom: 60rpx;
}

.go-shopping-btn {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 60rpx;
  font-size: 28rpx;
  font-weight: 500;
}

/* 底部结算栏 */
.checkout-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.checkout-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.total-price {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.price-highlight {
  font-size: 32rpx;
  font-weight: 600;
  color: #ff6b35;
}

.item-count {
  font-size: 22rpx;
  color: #999;
}

.checkout-btn {
  padding: 24rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  border: none;
  transition: all 0.3s ease;
}

.checkout-btn.active {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: #fff;
}

.checkout-btn.disabled {
  background: #f0f0f0;
  color: #999;
}

/* 安全区域 */
.safe-area {
  height: env(safe-area-inset-bottom);
}
