package com.ordering.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ordering.entity.Image;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 图片Mapper接口
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Mapper
public interface ImageMapper extends BaseMapper<Image> {

    /**
     * 根据分类统计图片数量
     * 
     * @param category 图片分类
     * @return 图片数量
     */
    @Select("SELECT COUNT(*) FROM images WHERE category = #{category} AND status = 1 AND deleted = 0")
    int countByCategory(@Param("category") String category);

    /**
     * 根据业务ID统计图片数量
     * 
     * @param businessId 业务ID
     * @return 图片数量
     */
    @Select("SELECT COUNT(*) FROM images WHERE business_id = #{businessId} AND status = 1 AND deleted = 0")
    int countByBusinessId(@Param("businessId") Long businessId);

    /**
     * 获取最近上传的图片
     * 
     * @param limit 限制数量
     * @return 图片列表
     */
    @Select("SELECT * FROM images WHERE status = 1 AND deleted = 0 ORDER BY created_at DESC LIMIT #{limit}")
    List<Image> getRecentImages(@Param("limit") int limit);

    /**
     * 根据分类获取图片（按排序和创建时间）
     * 
     * @param category 图片分类
     * @return 图片列表
     */
    @Select("SELECT * FROM images WHERE category = #{category} AND status = 1 AND deleted = 0 ORDER BY sort_order ASC, created_at DESC")
    List<Image> getImagesByCategory(@Param("category") String category);

    /**
     * 批量更新图片状态
     * 
     * @param ids 图片ID列表
     * @param status 状态
     * @return 更新数量
     */
    @Update("<script>" +
            "UPDATE images SET status = #{status}, updated_at = NOW() WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") Integer status);

    /**
     * 批量更新图片排序
     * 
     * @param ids 图片ID列表
     * @param sortOrders 排序值列表
     * @return 更新数量
     */
    @Update("<script>" +
            "<foreach collection='ids' item='id' index='index' separator=';'>" +
            "UPDATE images SET sort_order = #{sortOrders[${index}]}, updated_at = NOW() WHERE id = #{id}" +
            "</foreach>" +
            "</script>")
    int batchUpdateSortOrder(@Param("ids") List<Long> ids, @Param("sortOrders") List<Integer> sortOrders);

    /**
     * 根据文件路径查找图片
     * 
     * @param filePath 文件路径
     * @return 图片信息
     */
    @Select("SELECT * FROM images WHERE file_path = #{filePath} AND deleted = 0")
    Image getByFilePath(@Param("filePath") String filePath);

    /**
     * 获取指定分类下的所有图片URL
     * 
     * @param category 图片分类
     * @return URL列表
     */
    @Select("SELECT url FROM images WHERE category = #{category} AND status = 1 AND deleted = 0 ORDER BY sort_order ASC, created_at DESC")
    List<String> getUrlsByCategory(@Param("category") String category);

    /**
     * 获取指定业务ID下的所有图片URL
     * 
     * @param businessId 业务ID
     * @return URL列表
     */
    @Select("SELECT url FROM images WHERE business_id = #{businessId} AND status = 1 AND deleted = 0 ORDER BY sort_order ASC, created_at DESC")
    List<String> getUrlsByBusinessId(@Param("businessId") Long businessId);

    /**
     * 清理无效的图片记录（文件不存在）
     * 
     * @return 清理数量
     */
    @Update("UPDATE images SET deleted = 1, updated_at = NOW() WHERE deleted = 0 AND id IN (" +
            "SELECT id FROM (" +
            "SELECT i.id FROM images i WHERE i.deleted = 0 AND NOT EXISTS (" +
            "SELECT 1 FROM images i2 WHERE i2.file_path = i.file_path AND i2.deleted = 0" +
            ")" +
            ") AS temp" +
            ")")
    int cleanupInvalidImages();
}
