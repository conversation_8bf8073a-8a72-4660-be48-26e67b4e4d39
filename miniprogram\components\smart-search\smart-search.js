// components/smart-search/smart-search.js
Component({
  properties: {
    placeholder: {
      type: String,
      value: '搜索菜品'
    },
    hotKeywords: {
      type: Array,
      value: []
    },
    showHistory: {
      type: Boolean,
      value: true
    }
  },

  data: {
    searchValue: '',
    focused: false,
    searchHistory: [],
    suggestions: [],
    showSuggestions: false,
    animationData: {}
  },

  lifetimes: {
    attached() {
      this.loadSearchHistory()
      this.loadHotKeywords()
    }
  },

  methods: {
    // 输入框聚焦
    onFocus() {
      this.setData({ focused: true })
      this.showSearchPanel()
    },

    // 输入框失焦
    onBlur() {
      // 延迟隐藏，避免点击建议项时立即隐藏
      setTimeout(() => {
        this.setData({ 
          focused: false,
          showSuggestions: false 
        })
        this.hideSearchPanel()
      }, 200)
    },

    // 输入变化
    onInput(e) {
      const value = e.detail.value
      this.setData({ searchValue: value })
      
      if (value.trim()) {
        this.getSuggestions(value)
      } else {
        this.setData({ 
          suggestions: [],
          showSuggestions: false 
        })
      }
    },

    // 搜索确认
    onConfirm() {
      const { searchValue } = this.data
      if (searchValue.trim()) {
        this.performSearch(searchValue.trim())
      }
    },

    // 执行搜索
    performSearch(keyword) {
      // 保存搜索历史
      this.saveSearchHistory(keyword)
      
      // 触发搜索事件
      this.triggerEvent('search', { keyword })
      
      // 隐藏搜索面板
      this.setData({ 
        focused: false,
        showSuggestions: false 
      })
      this.hideSearchPanel()
    },

    // 点击建议项
    onSuggestionTap(e) {
      const { keyword } = e.currentTarget.dataset
      this.setData({ searchValue: keyword })
      this.performSearch(keyword)
    },

    // 点击热门关键词
    onHotKeywordTap(e) {
      const { keyword } = e.currentTarget.dataset
      this.setData({ searchValue: keyword })
      this.performSearch(keyword)
    },

    // 点击历史记录
    onHistoryTap(e) {
      const { keyword } = e.currentTarget.dataset
      this.setData({ searchValue: keyword })
      this.performSearch(keyword)
    },

    // 清除搜索历史
    onClearHistory() {
      wx.showModal({
        title: '提示',
        content: '确定要清除搜索历史吗？',
        success: (res) => {
          if (res.confirm) {
            this.setData({ searchHistory: [] })
            wx.removeStorageSync('searchHistory')
          }
        }
      })
    },

    // 获取搜索建议
    getSuggestions(keyword) {
      // 模拟搜索建议
      const mockSuggestions = [
        '宫保鸡丁', '麻婆豆腐', '红烧肉', '糖醋里脊', '鱼香肉丝',
        '口水鸡', '凉拌黄瓜', '西红柿鸡蛋汤', '扬州炒饭', '牛肉面'
      ]
      
      const suggestions = mockSuggestions
        .filter(item => item.includes(keyword))
        .slice(0, 5)
      
      this.setData({ 
        suggestions,
        showSuggestions: suggestions.length > 0 
      })
    },

    // 加载搜索历史
    loadSearchHistory() {
      const history = wx.getStorageSync('searchHistory') || []
      this.setData({ searchHistory: history })
    },

    // 保存搜索历史
    saveSearchHistory(keyword) {
      let history = this.data.searchHistory
      
      // 移除重复项
      history = history.filter(item => item !== keyword)
      
      // 添加到开头
      history.unshift(keyword)
      
      // 限制历史记录数量
      if (history.length > 10) {
        history = history.slice(0, 10)
      }
      
      this.setData({ searchHistory: history })
      wx.setStorageSync('searchHistory', history)
    },

    // 加载热门关键词
    loadHotKeywords() {
      if (this.data.hotKeywords.length === 0) {
        // 设置默认热门关键词
        this.setData({
          hotKeywords: ['川菜', '粤菜', '湘菜', '鲁菜', '素食', '甜品']
        })
      }
    },

    // 显示搜索面板
    showSearchPanel() {
      const animation = wx.createAnimation({
        duration: 300,
        timingFunction: 'ease-out'
      })
      
      animation.translateY(0).opacity(1).step()
      
      this.setData({
        animationData: animation.export()
      })
    },

    // 隐藏搜索面板
    hideSearchPanel() {
      const animation = wx.createAnimation({
        duration: 300,
        timingFunction: 'ease-in'
      })
      
      animation.translateY(-20).opacity(0).step()
      
      this.setData({
        animationData: animation.export()
      })
    },

    // 清除输入
    onClear() {
      this.setData({ 
        searchValue: '',
        suggestions: [],
        showSuggestions: false 
      })
    }
  }
})
