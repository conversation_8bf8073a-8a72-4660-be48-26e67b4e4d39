<!--pages/login/login.wxml-->
<view class="container">
  <!-- 顶部装饰 -->
  <view class="header-decoration">
    <view class="decoration-circle circle-1"></view>
    <view class="decoration-circle circle-2"></view>
    <view class="decoration-circle circle-3"></view>
  </view>

  <!-- 返回按钮 -->
  <view class="back-btn" bindtap="onBack">
    <text class="back-icon">←</text>
  </view>

  <!-- 登录卡片 -->
  <view class="login-card">
    <!-- Logo和标题 -->
    <view class="login-header">
      <view class="logo">
        <text class="logo-icon">🍽️</text>
      </view>
      <text class="app-name">美食天下</text>
      <text class="app-slogan">发现身边的美味</text>
    </view>

    <!-- 登录方式切换 -->
    <view class="login-tabs">
      <view 
        class="tab-item {{loginType === 'phone' ? 'active' : ''}}"
        bindtap="onSwitchLoginType"
        data-type="phone"
      >
        <text class="tab-text">手机登录</text>
      </view>
      <view 
        class="tab-item {{loginType === 'wechat' ? 'active' : ''}}"
        bindtap="onSwitchLoginType"
        data-type="wechat"
      >
        <text class="tab-text">微信登录</text>
      </view>
    </view>

    <!-- 手机号登录 -->
    <view class="login-form" wx:if="{{loginType === 'phone'}}">
      <view class="form-item">
        <view class="input-wrapper">
          <text class="input-icon">📱</text>
          <input 
            class="form-input"
            type="number"
            placeholder="请输入手机号"
            value="{{phone}}"
            bindinput="onPhoneInput"
            maxlength="11"
          />
        </view>
      </view>

      <view class="form-item">
        <view class="input-wrapper">
          <text class="input-icon">🔐</text>
          <input 
            class="form-input"
            type="number"
            placeholder="请输入验证码"
            value="{{code}}"
            bindinput="onCodeInput"
            maxlength="6"
          />
          <view 
            class="code-btn {{canSendCode ? '' : 'disabled'}}"
            bindtap="onSendCode"
          >
            <text class="code-text">
              {{canSendCode ? '发送验证码' : countdown + 's'}}
            </text>
          </view>
        </view>
      </view>

      <button 
        class="login-btn {{loading ? 'loading' : ''}}"
        bindtap="onPhoneLogin"
        disabled="{{loading}}"
      >
        <text class="btn-text">{{loading ? '登录中...' : '登录'}}</text>
      </button>
    </view>

    <!-- 微信登录 -->
    <view class="login-form" wx:if="{{loginType === 'wechat'}}">
      <view class="wechat-login-content">
        <view class="wechat-icon">
          <text class="wechat-logo">💬</text>
        </view>
        <text class="wechat-tip">使用微信账号快速登录</text>
        
        <button 
          class="wechat-login-btn {{loading ? 'loading' : ''}}"
          bindtap="onWechatLogin"
          disabled="{{loading}}"
        >
          <text class="btn-text">{{loading ? '登录中...' : '微信一键登录'}}</text>
        </button>
      </view>
    </view>

    <!-- 协议同意 -->
    <view class="agreement-section">
      <view class="agreement-checkbox" bindtap="onToggleAgreement">
        <view class="checkbox {{agreementChecked ? 'checked' : ''}}">
          <text class="checkbox-icon" wx:if="{{agreementChecked}}">✓</text>
        </view>
        <text class="agreement-text">
          我已阅读并同意
          <text class="agreement-link" bindtap="onViewAgreement">《用户协议》</text>
          和
          <text class="agreement-link" bindtap="onViewPrivacy">《隐私政策》</text>
        </text>
      </view>
    </view>
  </view>

  <!-- 底部装饰 -->
  <view class="footer-decoration">
    <view class="decoration-wave wave-1"></view>
    <view class="decoration-wave wave-2"></view>
    <view class="decoration-wave wave-3"></view>
  </view>
</view>
