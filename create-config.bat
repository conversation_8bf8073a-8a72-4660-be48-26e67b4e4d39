@echo off
chcp 65001 >nul
echo 正在创建 project.config.json 文件...

cd miniprogram

(
echo {
echo   "description": "WeChat Mini Program",
echo   "setting": {
echo     "urlCheck": false,
echo     "es6": true,
echo     "enhance": true,
echo     "postcss": true,
echo     "minified": true,
echo     "newFeature": false,
echo     "coverView": true,
echo     "nodeModules": false,
echo     "autoAudits": false,
echo     "showShadowRootInWxmlPanel": true,
echo     "scopeDataCheck": false,
echo     "checkInvalidKey": true,
echo     "checkSiteMap": true,
echo     "uploadWithSourceMap": true,
echo     "compileHotReLoad": false,
echo     "useMultiFrameRuntime": true,
echo     "useApiHook": true,
echo     "useApiHostProcess": true,
echo     "minifyWXSS": true,
echo     "minifyWXML": true
echo   },
echo   "compileType": "miniprogram",
echo   "libVersion": "3.8.5",
echo   "appid": "wx1234567890abcdef",
echo   "projectname": "ordering-system"
echo }
) > project.config.json

echo 文件创建完成！
echo 请使用微信开发者工具重新打开项目。
pause
